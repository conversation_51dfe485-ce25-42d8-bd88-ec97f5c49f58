# yaslc抓包脚本

## 概述

这是一套专门为yaslc程序设计的抓包脚本，用于在程序运行时自动抓取网络流量，当程序崩溃或退出时自动停止抓包。主要目的是捕获可能导致程序崩溃的网络流量，便于后续分析和调试。

## 文件列表

| 文件名 | 描述 |
|--------|------|
| `capture_yaslc.sh` | 完整功能的抓包脚本，包含详细日志和配置选项 |
| `quick_capture.sh` | 简化版抓包脚本，适合快速启动 |
| `test_environment.sh` | 环境测试脚本，检查运行环境是否正确配置 |
| `capture_config_example.sh` | 配置示例文件，展示各种配置选项 |
| `抓包脚本使用说明.md` | 详细的使用说明文档 |

## 快速开始

### 1. 环境检查

```bash
# 检查环境是否正确配置
sudo ./test_environment.sh
```

### 2. 快速启动

```bash
# 使用简化版脚本快速启动
sudo ./quick_capture.sh
```

### 3. 完整功能启动

```bash
# 使用完整功能脚本
sudo ./capture_yaslc.sh

# 查看帮助信息
sudo ./capture_yaslc.sh --help
```

## 主要功能

- ✅ **自动启动yaslc程序**
- ✅ **同时启动网络抓包**
- ✅ **每5分钟自动轮转抓包文件**
- ✅ **循环保存，自动清理旧文件**
- ✅ **程序崩溃时自动停止抓包**
- ✅ **完整的日志记录**
- ✅ **信号处理和优雅退出**
- ✅ **配置文件支持**
- ✅ **多种过滤器选项**

## 输出文件

### 抓包文件

- **位置**: `/root/pcaps/`
- **命名**: `yaslc_capture_YYYYMMDD_HHMMSS.pcap`
- **轮转**: 每5分钟创建新文件
- **清理**: 自动保留最新100个文件

### 日志文件

- **位置**: `/var/log/yaslc_capture.log`
- **内容**: 程序启动、抓包状态、错误信息等

## 使用场景

### 1. 程序崩溃调试

当yaslc程序出现崩溃时，抓包文件可以帮助分析：
- 崩溃前的网络流量
- 异常的数据包
- 网络协议错误

### 2. 性能问题分析

通过抓包文件可以分析：
- 网络延迟
- 丢包情况
- 流量模式

### 3. 协议兼容性测试

验证yaslc程序对不同设备的兼容性：
- 不同厂商的摄像头
- 不同版本的协议
- 异常的协议实现

## 常用命令

```bash
# 基本使用
sudo ./capture_yaslc.sh

# 指定网络接口
sudo ./capture_yaslc.sh -i eth0

# 添加过滤器，只抓取RTSP流量
sudo ./capture_yaslc.sh -f "port 554"

# 设置10分钟轮转，保留50个文件
sudo ./capture_yaslc.sh -t 600 -m 50

# 指定抓包目录
sudo ./capture_yaslc.sh -d /tmp/pcaps

# 组合使用
sudo ./capture_yaslc.sh -i eth0 -d /tmp/pcaps -f "host *************" -t 600 -m 50
```

## 分析抓包文件

### 使用Wireshark

```bash
# 安装Wireshark
sudo apt install wireshark

# 打开抓包文件
wireshark /root/pcaps/yaslc_capture_20250701_143022.pcap
```

### 使用tcpdump

```bash
# 查看抓包文件内容
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap

# 过滤RTSP流量
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap port 554

# 查看统计信息
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap | wc -l
```

## 故障排除

### 常见问题

1. **权限不足**
   - 解决：使用`sudo`运行脚本

2. **yaslc程序不存在**
   - 解决：运行`./build.sh`编译程序

3. **网络接口错误**
   - 解决：使用`ip link show`查看可用接口

4. **磁盘空间不足**
   - 解决：清理旧文件或增加磁盘空间

### 调试步骤

1. 运行环境测试：`sudo ./test_environment.sh`
2. 检查日志文件：`tail -f /var/log/yaslc_capture.log`
3. 手动测试tcpdump：`sudo tcpdump -i ens33 -c 10`
4. 检查进程状态：`ps aux | grep yaslc`

## 注意事项

1. **权限要求**: 必须以root权限运行
2. **磁盘空间**: 确保有足够的磁盘空间存储抓包文件
3. **网络负载**: 在高流量环境下可能影响系统性能
4. **时间同步**: 确保系统时间正确，便于分析

## 技术支持

如果遇到问题，请：

1. 查看详细使用说明：`抓包脚本使用说明.md`
2. 运行环境测试：`sudo ./test_environment.sh`
3. 查看日志文件：`tail -f /var/log/yaslc_capture.log`
4. 检查配置文件：`cat ./run/config.ini`

## 版本信息

- **版本**: 1.0
- **更新日期**: 2025-07-01
- **支持系统**: Linux
- **依赖**: tcpdump, yaslc程序

---

**重要提示**: 这些脚本专门为调试yaslc程序崩溃问题而设计，请在测试环境中使用，避免在生产环境中长时间运行抓包。
