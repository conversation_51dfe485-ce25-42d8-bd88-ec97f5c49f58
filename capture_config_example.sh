#!/bin/bash

# yaslc抓包脚本配置示例
# 复制此文件并根据需要修改配置

# =============================================================================
# 基本配置
# =============================================================================

# yaslc程序路径
YASLC_PATH="./run/yaslc"

# 抓包文件保存目录
PCAP_DIR="/root/pcaps"

# 网络接口（如果不指定，将从config.ini读取）
INTERFACE="ens33"

# 文件轮转间隔（秒）
# 300 = 5分钟, 600 = 10分钟, 1800 = 30分钟
ROTATE_INTERVAL=300

# 最大保留文件数
MAX_FILES=100

# 日志文件路径
LOG_FILE="/var/log/yaslc_capture.log"

# =============================================================================
# 抓包过滤器配置
# =============================================================================

# tcpdump过滤器，用于只抓取特定的流量
# 留空表示抓取所有流量

# 示例1: 抓取特定主机的流量
# TCPDUMP_FILTER="host *************"

# 示例2: 抓取特定端口的流量
# TCPDUMP_FILTER="port 554 or port 8554"

# 示例3: 抓取RTP流量（UDP端口范围）
# TCPDUMP_FILTER="udp and portrange 10000-65535"

# 示例4: 抓取TCP流量
# TCPDUMP_FILTER="tcp"

# 示例5: 排除SSH流量
# TCPDUMP_FILTER="not port 22"

# 示例6: 复合过滤器
# TCPDUMP_FILTER="(host ************* or host *************) and not port 22"

# 默认：抓取所有流量
TCPDUMP_FILTER=""

# =============================================================================
# 高级配置
# =============================================================================

# 抓包文件大小限制（MB）
# 注意：这个参数需要tcpdump支持-C选项
# MAX_FILE_SIZE=100

# 抓包缓冲区大小（KB）
# BUFFER_SIZE=1024

# 是否启用详细日志
VERBOSE_LOG=true

# 是否在程序退出后保留抓包文件
KEEP_PCAP_ON_EXIT=true

# 是否启用文件压缩（需要gzip）
ENABLE_COMPRESSION=false

# =============================================================================
# 监控配置
# =============================================================================

# yaslc程序状态检查间隔（秒）
MONITOR_INTERVAL=5

# 程序崩溃后的等待时间（秒）
CRASH_WAIT_TIME=10

# 是否在程序崩溃后自动重启
AUTO_RESTART=false

# 最大重启次数
MAX_RESTART_COUNT=3

# =============================================================================
# 通知配置
# =============================================================================

# 是否启用邮件通知（需要配置sendmail）
ENABLE_EMAIL_NOTIFICATION=false
EMAIL_RECIPIENT="<EMAIL>"

# 是否启用系统日志
ENABLE_SYSLOG=true

# =============================================================================
# 使用示例
# =============================================================================

# 1. 基本使用
# ./capture_yaslc.sh

# 2. 指定网络接口
# ./capture_yaslc.sh -i eth0

# 3. 指定抓包目录
# ./capture_yaslc.sh -d /tmp/pcaps

# 4. 添加过滤器，只抓取RTSP流量
# ./capture_yaslc.sh -f "port 554"

# 5. 设置30分钟轮转，保留20个文件
# ./capture_yaslc.sh -t 1800 -m 20

# 6. 组合使用
# ./capture_yaslc.sh -i eth0 -d /tmp/pcaps -f "host *************" -t 600 -m 50

# =============================================================================
# 常用过滤器模板
# =============================================================================

# RTSP流量
# FILTER_RTSP="port 554 or port 8554"

# RTP流量（常用端口范围）
# FILTER_RTP="udp and portrange 10000-65535"

# HTTP流量
# FILTER_HTTP="port 80 or port 8080 or port 443"

# 特定摄像头IP
# FILTER_CAMERA="host *************"

# 排除本地流量
# FILTER_NO_LOCAL="not host 127.0.0.1 and not host ::1"

# 只抓取错误包（需要高级过滤）
# FILTER_ERRORS="icmp or (tcp[tcpflags] & tcp-rst != 0)"

# =============================================================================
# 性能优化建议
# =============================================================================

# 1. 对于高流量环境，建议：
#    - 使用更短的轮转间隔（如60秒）
#    - 减少最大文件数
#    - 使用更具体的过滤器

# 2. 对于低流量环境，建议：
#    - 使用更长的轮转间隔（如1800秒）
#    - 增加最大文件数
#    - 可以不使用过滤器

# 3. 磁盘空间优化：
#    - 启用文件压缩
#    - 定期清理旧文件
#    - 监控磁盘使用情况

# =============================================================================
# 故障排除
# =============================================================================

# 如果遇到问题，可以尝试：

# 1. 检查网络接口
# ip link show

# 2. 测试tcpdump
# sudo tcpdump -i ens33 -c 10

# 3. 检查权限
# ls -la /root/pcaps

# 4. 查看日志
# tail -f /var/log/yaslc_capture.log

# 5. 检查进程
# ps aux | grep yaslc
# ps aux | grep tcpdump
