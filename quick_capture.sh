#!/bin/bash

# 快速抓包脚本 - 简化版本
# 启动yaslc程序并同时抓包，程序退出时自动停止抓包

# 基本配置
YASLC_PATH="./run/yaslc"
PCAP_DIR="/root/pcaps"
INTERFACE="ens33"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# 创建抓包目录
mkdir -p "$PCAP_DIR"

echo "========== 快速抓包脚本 =========="
echo "时间: $(date)"
echo "yaslc路径: $YASLC_PATH"
echo "抓包目录: $PCAP_DIR"
echo "网络接口: $INTERFACE"
echo "================================="

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    exit 1
fi

# 检查yaslc程序
if [ ! -f "$YASLC_PATH" ]; then
    echo "错误: yaslc程序不存在: $YASLC_PATH"
    exit 1
fi

# 清理函数
cleanup() {
    echo ""
    echo "正在停止进程..."
    
    if [ ! -z "$TCPDUMP_PID" ]; then
        kill "$TCPDUMP_PID" 2>/dev/null
        echo "已停止抓包进程"
    fi
    
    if [ ! -z "$YASLC_PID" ]; then
        kill "$YASLC_PID" 2>/dev/null
        echo "已停止yaslc进程"
    fi
    
    echo "清理完成"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 启动yaslc程序
echo "启动yaslc程序..."
cd "$(dirname "$YASLC_PATH")"
"$YASLC_PATH" &
YASLC_PID=$!

sleep 2

# 检查yaslc是否启动成功
if ! kill -0 "$YASLC_PID" 2>/dev/null; then
    echo "错误: yaslc程序启动失败"
    exit 1
fi

echo "yaslc程序启动成功 (PID: $YASLC_PID)"

# 启动抓包
echo "启动抓包..."
PCAP_FILE="$PCAP_DIR/yaslc_capture_${TIMESTAMP}.pcap"

# 使用tcpdump抓包，每5分钟轮转一次文件
tcpdump -i "$INTERFACE" -w "$PCAP_FILE" -G 300 -W 100 &
TCPDUMP_PID=$!

sleep 2

# 检查tcpdump是否启动成功
if ! kill -0 "$TCPDUMP_PID" 2>/dev/null; then
    echo "错误: 抓包启动失败"
    kill "$YASLC_PID" 2>/dev/null
    exit 1
fi

echo "抓包启动成功 (PID: $TCPDUMP_PID)"
echo "抓包文件: $PCAP_FILE"
echo ""
echo "监控yaslc程序状态... (按Ctrl+C停止)"

# 监控yaslc程序
while kill -0 "$YASLC_PID" 2>/dev/null; do
    sleep 5
done

echo ""
echo "检测到yaslc程序已退出"
cleanup
