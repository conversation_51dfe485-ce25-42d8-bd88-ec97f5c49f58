# yaslc抓包脚本使用说明

## 概述

本项目提供了两个抓包脚本，用于在yaslc程序运行时自动抓包，当程序崩溃或退出时自动停止抓包。这些脚本的目的是捕获可能导致程序崩溃的网络流量。

## 脚本文件

### 1. capture_yaslc.sh (完整版)
功能丰富的抓包脚本，包含完整的日志记录、错误处理和配置选项。

### 2. quick_capture.sh (简化版)
简化版本的抓包脚本，适合快速启动和测试。

## 功能特性

- ✅ 自动启动yaslc程序
- ✅ 同时启动网络抓包
- ✅ 每5分钟自动轮转抓包文件
- ✅ 循环保存，自动清理旧文件
- ✅ 程序崩溃时自动停止抓包
- ✅ 完整的日志记录
- ✅ 信号处理和优雅退出
- ✅ 配置文件支持

## 使用方法

### 准备工作

1. **确保以root权限运行**（抓包需要管理员权限）
2. **确保yaslc程序已编译**
3. **确保/root/pcaps目录可写**

### 快速启动（推荐）

```bash
# 使用简化版脚本
sudo ./quick_capture.sh
```

### 完整功能启动

```bash
# 使用默认配置
sudo ./capture_yaslc.sh

# 指定网络接口
sudo ./capture_yaslc.sh -i eth0

# 指定抓包目录
sudo ./capture_yaslc.sh -d /tmp/pcaps

# 添加抓包过滤器
sudo ./capture_yaslc.sh -f "host *************"

# 设置10分钟轮转，保留50个文件
sudo ./capture_yaslc.sh -t 600 -m 50
```

### 命令行选项（完整版）

```
选项:
  -h, --help          显示帮助信息
  -i, --interface     指定网络接口（默认从config.ini读取）
  -d, --dir           指定抓包文件保存目录（默认: /root/pcaps）
  -t, --interval      指定文件轮转间隔（秒，默认: 300）
  -f, --filter        指定tcpdump过滤器
  -m, --max-files     最大保留文件数（默认: 100）
```

## 配置说明

### 默认配置

- **yaslc程序路径**: `./run/yaslc`
- **抓包目录**: `/root/pcaps`
- **网络接口**: 从`./run/config.ini`读取，默认`ens33`
- **文件轮转间隔**: 300秒（5分钟）
- **最大文件数**: 100个
- **日志文件**: `/var/log/yaslc_capture.log`

### 网络接口配置

脚本会自动从`./run/config.ini`文件中读取网络接口配置：

```ini
IF = ens33    # 网络接口名
```

如果配置文件不存在或接口配置无效，可以通过命令行参数指定。

## 输出文件

### 抓包文件命名规则

```
yaslc_capture_YYYYMMDD_HHMMSS.pcap
```

例如：`yaslc_capture_20250701_143022.pcap`

### 文件轮转

- 每5分钟（300秒）自动创建新的抓包文件
- 自动清理超过最大数量限制的旧文件
- 文件按时间戳命名，便于分析

## 日志记录

完整版脚本提供详细的日志记录：

- **日志文件**: `/var/log/yaslc_capture.log`
- **日志级别**: INFO, WARN, ERROR, SUCCESS
- **日志内容**: 程序启动、抓包状态、错误信息等

### 查看日志

```bash
# 查看实时日志
tail -f /var/log/yaslc_capture.log

# 查看最近的日志
tail -100 /var/log/yaslc_capture.log
```

## 故障排除

### 常见问题

1. **权限不足**
   ```
   错误: 请以root权限运行此脚本
   ```
   解决：使用`sudo`运行脚本

2. **yaslc程序不存在**
   ```
   错误: yaslc程序不存在: ./run/yaslc
   ```
   解决：确保已编译yaslc程序，或检查路径是否正确

3. **网络接口不存在**
   ```
   错误: 网络接口不存在: ens33
   ```
   解决：使用`ip link show`查看可用接口，然后用`-i`参数指定正确的接口

4. **抓包启动失败**
   ```
   错误: 抓包启动失败
   ```
   解决：检查是否有足够的权限，网络接口是否正确

### 调试方法

1. **检查网络接口**
   ```bash
   ip link show
   ```

2. **手动测试tcpdump**
   ```bash
   sudo tcpdump -i ens33 -c 10
   ```

3. **检查yaslc程序**
   ```bash
   ./run/yaslc --help
   ```

4. **查看进程状态**
   ```bash
   ps aux | grep yaslc
   ps aux | grep tcpdump
   ```

## 停止脚本

### 正常停止

按`Ctrl+C`发送SIGINT信号，脚本会优雅地停止所有进程。

### 强制停止

如果脚本无响应，可以强制终止：

```bash
# 查找进程
ps aux | grep capture_yaslc
ps aux | grep yaslc
ps aux | grep tcpdump

# 强制终止
sudo kill -9 <PID>
```

## 分析抓包文件

### 使用Wireshark

```bash
# 安装Wireshark
sudo apt install wireshark

# 打开抓包文件
wireshark /root/pcaps/yaslc_capture_20250701_143022.pcap
```

### 使用tcpdump分析

```bash
# 查看抓包文件内容
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap

# 过滤特定协议
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap tcp

# 查看统计信息
tcpdump -r /root/pcaps/yaslc_capture_20250701_143022.pcap | wc -l
```

## 注意事项

1. **磁盘空间**: 抓包文件可能很大，确保有足够的磁盘空间
2. **网络负载**: 在高流量环境下，抓包可能影响系统性能
3. **文件权限**: 确保抓包目录有写权限
4. **防火墙**: 确保防火墙不会阻止抓包
5. **时间同步**: 确保系统时间正确，便于分析时间相关的问题

## 版本信息

- **脚本版本**: 1.0
- **支持的系统**: Linux
- **依赖工具**: tcpdump, yaslc
- **最后更新**: 2025-07-01
