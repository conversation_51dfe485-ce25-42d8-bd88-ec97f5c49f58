
#ifndef _SLC_FLOW_H_
#define _SLC_FLOW_H_
#include "ipc/ipc.h"
#include "slc_typedefs.h"
#include <stdint.h>
#include <cstdint>
#include <ctime>
#include <functional>
#include <map>
#include <mutex>
#include <stdint.h>
#include "tcp_rsm/tcp_rsm.h"
#include <unordered_map>
#include <sys/time.h>
#include <iostream>

#define CACHE_MAX 10  //协议未识别前 最多缓存多少?

template <typename T1, typename T2, typename T3, typename T4, typename T5, typename T6>
struct Node;
struct hash_func {
  template <class T1, class T2, class T3, class T4, class T5, class T6>
  std::size_t operator()(const Node<T1, T2, T3, T4, T5, T6> &node) const {
    size_t h1 = std::hash<T1>()(node.t1);
    size_t h2 = std::hash<T2>()(node.t2);
    size_t h3 = std::hash<T3>()(node.t3);
    size_t h4 = std::hash<T4>()(node.t4);
    size_t h5 = std::hash<T5>()(node.t5);
    size_t h6 = std::hash<T6>()(node.t6);

    // 使用改进的hash组合算法，避免XOR的对称性问题
    // 基于boost::hash_combine的思想，但加入位置权重
    size_t result = h1;
    result = result * 31 + h2;  // 使用质数31作为乘数
    result = result * 37 + h3;  // 使用不同的质数避免模式
    result = result * 41 + h4;
    result = result * 43 + h5;
    result = result * 47 + h6;

    // 额外的混合步骤，确保所有位都参与运算
    result ^= (result >> 16);
    result *= 0x85ebca6b;
    result ^= (result >> 13);
    result *= 0xc2b2ae35;
    result ^= (result >> 16);

    return result;
  }
};

template <typename T1, typename T2, typename T3, typename T4, typename T5, typename T6>
struct Node {
  T1 t1;
  T2 t2;
  T3 t3;
  T4 t4;
  T5 t5;
  T6 t6;

  Node(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6) {
    this->t1 = t1;
    this->t2 = t2;

    this->t3 = t3;
    this->t4 = t4;
    this->t5 = t5;
    this->t6 = t6;
  }

  bool operator==(const Node &p) const {
    // 正确的相等性比较：逐个比较所有字段
    return t1 == p.t1 && t2 == p.t2 && t3 == p.t3 &&
           t4 == p.t4 && t5 == p.t5 && t6 == p.t6;
  }
};
struct pkt_info {
  const uint8_t            *raw_pkt;
  const struct slc_ethhdr  *ethhdr;
  const struct slc_iphdr   *iph;
  const struct slc_ipv6hdr *iph6;
  const struct slc_tcphdr  *tcph;
  const struct slc_udphdr  *udph;
  uint8_t                   proto;
  uint8_t                   ipversion;
  int                       pkt_len;
};

struct five_tuple {
  uint8_t  ip_src[16];
  uint8_t  ip_dst[16];
  uint16_t port_src;
  uint16_t port_dst;
  uint8_t  ip_version;
  uint8_t  proto;
};

struct tcp_rsm_cache {
  uint8_t  c2s;
  uint32_t seq;
  uint8_t  flag;
  uint32_t ack_seq;
};
enum class RtpMediaType {
  unknown,
  audio,
  video_h264,
  video_h261,
  video_h263,
  raw,
};

#define MAX_CACHE 20
typedef struct flow_info {
  struct five_tuple tuple;  //正向五元组

  IPC_PROTOCOL_TYPE        real_protocol_id;  //协议识别后的应用层协议id
  IPC_PROTOCOL_TYPE        create_by;  //协议识别后的应用层协议id
  RtpMediaType             create_by_mediaType;
  uint16_t                 packet_stage;      //RTP识别包数 连续3包确认为RTP
  uint8_t                  userdata[8];       //应用层协议的关键信息,放在这里以免申请app_session
  const struct slc_tcphdr *tcph;

  /**************** 暂存TCP报文 **********************/

  struct tcp_rsm_cache cache[MAX_CACHE];
  int                  cache_num;
  /**************** 暂存TCP报文 **********************/
  struct tcp_rsm_t *rsm = NULL;  // 新版 TCP 重组接口

  void *user;
} slc_flow_info_t;

struct FuncPair {
  std::function<void(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)> identify_func;
  std::function<void(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)> dissect_func;

  std::function<int(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg)>
                                                                                    dissect_rsm_func;
  std::function<int(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len)> dissect_miss_func;
};

#endif