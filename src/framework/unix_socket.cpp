#include "unix_socket.h"
#include <iostream>
#include <cstring>
#include <string>
#include <pthread.h>
#include <sys/epoll.h>
#include <sys/select.h>
#include <cstdio>
#include <errno.h>
#include <fcntl.h>
#include <mutex>
#include <algorithm>
#include <vector>



// 静态函数作为pthread入口点
static void* process_thread_entry(void* arg) {
    UnixSocketServer* server = static_cast<UnixSocketServer*>(arg);
    server->process_loop_wrapper();
    return nullptr;
}

static void* accept_thread_entry(void* arg) {
    UnixSocketServerStream* server = static_cast<UnixSocketServerStream*>(arg);
    server->accept_loop_wrapper();
    return nullptr;
}

static void* receive_thread_entry(void* arg) {
    UnixSocketClient* client = static_cast<UnixSocketClient*>(arg);
  printf("pthread cerat %p \n",client);
    client->receive_loop_wrapper();
    return nullptr;
}

// UnixSocketServer 基类实现
UnixSocketServer::UnixSocketServer(const std::string& socket_path, SocketType type)
    : socket_path_(socket_path), server_fd_(-1), running_(false),
      socket_type_(type), sequence_counter_(0) ,buffer_(MAX_MESSAGE_SIZE){
    process_thread_ = 0;  // 初始化pthread_t
}

UnixSocketServer::~UnixSocketServer() {
    stop();
}

void UnixSocketServer::process_loop_wrapper() {
    process_loop();
}

bool UnixSocketServer::start() {
    if (running_.load()) {
        return true;
    }
    
    if (!setup_server_socket()) {
        return false;
    }
    
    // DGRAM套接字不需要epoll设置
    
    running_.store(true);

    // DGRAM套接字只需要消息处理线程
    if (pthread_create(&process_thread_, nullptr, process_thread_entry, this) != 0) {
        printf("Failed to create process thread\n");
        running_.store(false);
        cleanup();
        return false;
    }
    
    printf("Unix Socket Server started on: %s\n", socket_path_.c_str());
    return true;
}

void UnixSocketServer::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);

    // 等待消息处理线程结束
    if (process_thread_ != 0) {
        pthread_join(process_thread_, nullptr);
        process_thread_ = 0;
    }
    
    cleanup();
    printf("Unix Socket Server stopped\n");
}

void UnixSocketServer::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

// ============================================================================
// UnixSocketServerDgram 实现（DGRAM套接字，用于NALU传输）
// ============================================================================

UnixSocketServerDgram::UnixSocketServerDgram(const std::string& socket_path)
    : UnixSocketServer(socket_path, SocketType::DGRAM) {
    memset(&current_client_addr_, 0, sizeof(current_client_addr_));
    current_client_len_ = 0;
}

UnixSocketServerDgram::~UnixSocketServerDgram() {
}

bool UnixSocketServerDgram::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(client_fd, header, data);
}

void UnixSocketServerDgram::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    // DGRAM套接字的广播：发送到最后一个已知的客户端地址
    // 如果没有客户端地址，则忽略广播
    if (current_client_len_ > 0) {
        printf("Broadcasting DGRAM message type %u to client, length=%u\n", msg_type, length);
        ipc_msg_hdr header;
        header.proto_version = 10;  // v1.0
        header.msg_type = msg_type;
        header.msg_len = length;
        header.transaction_id = sequence_counter_.fetch_add(1);

        // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
        size_t total_size = sizeof(header) + length;
        std::vector<uint8_t> message_buffer(total_size);

        // 拷贝消息头
        memcpy(message_buffer.data(), &header, sizeof(header));

        // 拷贝消息体（如果有）
        if (length > 0 && data) {
            memcpy(message_buffer.data() + sizeof(header), data, length);
        }

        // 一次性发送完整消息
        ssize_t sent = sendto(server_fd_, message_buffer.data(), total_size, 0,
                             (struct sockaddr*)&current_client_addr_, current_client_len_);
        if (sent == -1) {
            printf("Failed to send DGRAM message: %s\n", strerror(errno));
        } else if (sent != static_cast<ssize_t>(total_size)) {
            printf("Partial DGRAM message sent: %zd/%zu bytes\n", sent, total_size);
        } else {
            printf("Successfully sent DGRAM message: %zd bytes\n", sent);
        }
    } else {
        printf("No client address recorded, cannot broadcast DGRAM message type %u\n", msg_type);
    }
}

size_t UnixSocketServerDgram::get_client_count() const {
    // DGRAM套接字没有持久连接，返回是否有已知客户端地址
    return (current_client_len_ > 0) ? 1 : 0;
}

bool UnixSocketServerDgram::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());

    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (server_fd_ == -1) {
        printf("Failed to create socket: %s\n", strerror(errno));
        return false;
    }

    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        printf("Failed to bind socket: %s\n", strerror(errno));
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    return true;
}

void UnixSocketServerDgram::cleanup() {
    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 删除socket文件
    unlink(socket_path_.c_str());
}

bool UnixSocketServerDgram::send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data) {
    // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
    size_t total_size = sizeof(header) + header.msg_len;
    std::vector<uint8_t> message_buffer(total_size);

    // 拷贝消息头
    memcpy(message_buffer.data(), &header, sizeof(header));

    // 拷贝消息体（如果有）
    if (header.msg_len > 0 && data) {
        memcpy(message_buffer.data() + sizeof(header), data, header.msg_len);
    }

    // 一次性发送完整消息到最后已知的客户端地址
    if (current_client_len_ > 0) {
        ssize_t sent = sendto(server_fd_, message_buffer.data(), total_size, 0,
                             (struct sockaddr*)&current_client_addr_, current_client_len_);
        return sent == static_cast<ssize_t>(total_size);
    }

    return false;
}

void UnixSocketServerDgram::process_loop() {
    struct sockaddr_un client_addr;
    socklen_t client_len;

    while (running_.load()) {
        client_len = sizeof(client_addr);

        // 使用recvfrom接收数据报，设置超时
        struct timeval timeout;
        timeout.tv_sec = 1;  // 1秒超时
        timeout.tv_usec = 0;

        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(server_fd_, &readfds);

        int result = select(server_fd_ + 1, &readfds, NULL, NULL, &timeout);
        if (result == -1) {
            if (errno != EINTR && running_.load()) {
                printf("select failed: %s\n", strerror(errno));
            }
            continue;
        } else if (result == 0) {
            // 超时，继续循环
            continue;
        }

        if (FD_ISSET(server_fd_, &readfds)) {
            ssize_t bytes_read = recvfrom(server_fd_, buffer_.data(), buffer_.size(), 0,
                                        (struct sockaddr*)&client_addr, &client_len);

            if (bytes_read > 0) {
                // 解析消息，使用server_fd_作为虚拟客户端ID
                parse_message(server_fd_, buffer_.data(), bytes_read);

                // 记录客户端地址用于回复
                current_client_addr_ = client_addr;
                current_client_len_ = client_len;
            } else if (bytes_read == -1 && errno != EAGAIN && errno != EWOULDBLOCK) {
                printf("recvfrom failed: %s\n", strerror(errno));
            }
        }
    }
}

// DGRAM套接字不需要单独的receive_message函数

bool UnixSocketServer::parse_message(int client_fd, const uint8_t* buffer, size_t length) {
    if (length < sizeof(ipc_msg_hdr)) {
        printf("Message too short\n");
        return false;
    }

    const ipc_msg_hdr* header = reinterpret_cast<const ipc_msg_hdr*>(buffer);

    if (header->proto_version != 10) {
        printf("Invalid protocol version\n");
        return false;
    }

    if (header->msg_len > MAX_MESSAGE_SIZE - sizeof(ipc_msg_hdr)) {
        printf("Message too large\n");
        return false;
    }

    if (length < sizeof(ipc_msg_hdr) + header->msg_len) {
        printf("Incomplete message\n");
        return false;
    }

    const uint8_t* data = buffer + sizeof(ipc_msg_hdr);

    if (message_handler_) {
        message_handler_(header->msg_type, data, header->msg_len, client_fd);
    }

    return true;
}

// ============================================================================
// UnixSocketServerStream 实现（STREAM套接字，用于控制消息）
// ============================================================================

UnixSocketServerStream::UnixSocketServerStream(const std::string& socket_path)
    : UnixSocketServer(socket_path, SocketType::STREAM), epoll_fd_(-1) {
    accept_thread_ = 0;  // 初始化pthread_t
}

void UnixSocketServerStream::accept_loop_wrapper() {
    accept_loop();
}

UnixSocketServerStream::~UnixSocketServerStream() {
}

bool UnixSocketServerStream::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(client_fd, header, data);
}

void UnixSocketServerStream::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    std::lock_guard<std::mutex> lock(clients_mutex_);
    for (int client_fd : client_fds_) {
        send_message_internal(client_fd, header, data);
    }
}

size_t UnixSocketServerStream::get_client_count() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    return client_fds_.size();
}

bool UnixSocketServerStream::send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体
    if (header.msg_len > 0 && data) {
        sent = send(client_fd, data, header.msg_len, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketServerStream::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());

    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (server_fd_ == -1) {
        printf("Failed to create socket: %s\n", strerror(errno));
        return false;
    }

    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        printf("Failed to bind socket: %s\n", strerror(errno));
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 监听连接
    if (listen(server_fd_, 10) == -1) {
        printf("Failed to listen: %s\n", strerror(errno));
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 创建epoll
    epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
    if (epoll_fd_ == -1) {
        printf("Failed to create epoll: %s\n", strerror(errno));
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 添加服务器socket到epoll
    struct epoll_event ev;
    ev.events = EPOLLIN;
    ev.data.fd = server_fd_;
    if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, server_fd_, &ev) == -1) {
        printf("Failed to add server socket to epoll: %s\n", strerror(errno));
        close(epoll_fd_);
        close(server_fd_);
        epoll_fd_ = -1;
        server_fd_ = -1;
        return false;
    }

    return true;
}

void UnixSocketServerStream::process_loop() {
    // 启动accept线程
    if (pthread_create(&accept_thread_, nullptr, accept_thread_entry, this) != 0) {
        printf("Failed to create accept thread\n");
        return;
    }

    const int MAX_EVENTS = 10;
    struct epoll_event events[MAX_EVENTS];

    while (running_.load()) {
        int nfds = epoll_wait(epoll_fd_, events, MAX_EVENTS, 1000);
        if (nfds == -1) {
            if (errno != EINTR && running_.load()) {
                printf("epoll_wait failed: %s\n", strerror(errno));
            }
            continue;
        }

        for (int i = 0; i < nfds; i++) {
            int fd = events[i].data.fd;
            if (fd != server_fd_) {
                // 客户端数据
                if (events[i].events & (EPOLLIN | EPOLLHUP | EPOLLERR)) {
                    if (!receive_message(fd)) {
                        remove_client(fd);
                    }
                }
            }
        }
    }

    // 等待accept线程结束
    if (accept_thread_ != 0) {
        pthread_join(accept_thread_, nullptr);
        accept_thread_ = 0;
    }
}

void UnixSocketServerStream::cleanup() {
    // 关闭所有客户端连接
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        for (int client_fd : client_fds_) {
            close(client_fd);
        }
        client_fds_.clear();
    }

    // 关闭epoll
    if (epoll_fd_ != -1) {
        close(epoll_fd_);
        epoll_fd_ = -1;
    }

    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 删除socket文件
    unlink(socket_path_.c_str());
}

void UnixSocketServerStream::accept_loop() {
    while (running_.load()) {
        struct sockaddr_un client_addr;
        socklen_t client_len = sizeof(client_addr);

        int client_fd = accept(server_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd == -1) {
            if (errno != EINTR && running_.load()) {
                printf("accept failed: %s\n", strerror(errno));
            }
            continue;
        }

        add_client(client_fd);
    }
}

void UnixSocketServerStream::add_client(int client_fd) {
    // 添加到客户端列表
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        client_fds_.push_back(client_fd);
    }

    // 添加到epoll
    struct epoll_event ev;
    ev.events = EPOLLIN | EPOLLHUP | EPOLLERR;
    ev.data.fd = client_fd;
    if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, client_fd, &ev) == -1) {
        printf("Failed to add client to epoll: %s\n", strerror(errno));
        remove_client(client_fd);
    }
}

void UnixSocketServerStream::remove_client(int client_fd) {
    // 从epoll移除
    epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, client_fd, nullptr);

    // 关闭socket
    close(client_fd);

    // 从客户端列表移除
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        client_fds_.erase(std::remove(client_fds_.begin(), client_fds_.end(), client_fd), client_fds_.end());
    }
}

bool UnixSocketServerStream::receive_message(int client_fd) {
    ssize_t received = recv(client_fd, buffer_.data(), buffer_.size(), 0);

    if (received <= 0) {
        return false;
    }

    return parse_message(client_fd, buffer_.data(), received);
}

// ============================================================================
// UnixSocketClient 基类实现
// ============================================================================

UnixSocketClient::UnixSocketClient(const std::string& socket_path, SocketType type)
    : socket_path_(socket_path), client_fd_(-1), connected_(false),
      receiving_(false), socket_type_(type), sequence_counter_(0) ,buffer_(MAX_MESSAGE_SIZE){
    receive_thread_ = 0;  // 初始化pthread_t
}

UnixSocketClient::~UnixSocketClient() {
    disconnect();
}



void UnixSocketClient::disconnect() {
    if (!connected_.load()) {
        return;
    }

    stop_async_receive();

    connected_.store(false);

    if (client_fd_ != -1) {
        close(client_fd_);
        client_fd_ = -1;
    }

    printf("Disconnected from Unix Socket Server\n");
}

void UnixSocketClient::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

void UnixSocketClient::receive_loop_wrapper() {
    receive_loop();
}

void UnixSocketClient::start_async_receive() {
    if (receiving_.load() || !connected_.load()) {
        return;
    }
    // sleep(5);
    receiving_.store(true);
    if (pthread_create(&receive_thread_, nullptr, receive_thread_entry, this) != 0) {
        printf("Failed to create receive thread\n");
        receiving_.store(false);
        return;
    }
}

void UnixSocketClient::stop_async_receive() {
    if (!receiving_.load()) {
        return;
    }

    receiving_.store(false);

    if (receive_thread_ != 0) {
        pthread_join(receive_thread_, nullptr);
        receive_thread_ = 0;
    }
}

// ============================================================================
// UnixSocketClientDgram 实现（DGRAM套接字，用于NALU接收）
// ============================================================================

UnixSocketClientDgram::UnixSocketClientDgram(const std::string& socket_path)
    : UnixSocketClient(socket_path, SocketType::DGRAM) {
    memset(&server_addr_, 0, sizeof(server_addr_));
}

UnixSocketClientDgram::~UnixSocketClientDgram() {
}

bool UnixSocketClientDgram::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (client_fd_ == -1) {
        printf("Failed to create DGRAM client socket: %s\n", strerror(errno));
        return false;
    }

    // 生成客户端socket地址：将服务器地址的.sock替换为_cli.sock
    client_socket_path_ = socket_path_;
    size_t pos = client_socket_path_.find(".sock");
    if (pos != std::string::npos) {
        client_socket_path_.replace(pos, 5, "_cli.sock");
    } else {
        client_socket_path_ += "_cli";
    }

    // 删除已存在的客户端socket文件
    unlink(client_socket_path_.c_str());

    // 绑定客户端socket到本地地址
    struct sockaddr_un client_addr;
    memset(&client_addr, 0, sizeof(client_addr));
    client_addr.sun_family = AF_UNIX;
    strncpy(client_addr.sun_path, client_socket_path_.c_str(), sizeof(client_addr.sun_path) - 1);

    if (bind(client_fd_, (struct sockaddr*)&client_addr, sizeof(client_addr)) == -1) {
        printf("Failed to bind DGRAM client socket to %s: %s\n", client_socket_path_.c_str(), strerror(errno));
        close(client_fd_);
        client_fd_ = -1;
        return false;
    }

    // 设置服务器地址（DGRAM不需要connect，只需要保存地址）
    server_addr_.sun_family = AF_UNIX;
    strncpy(server_addr_.sun_path, socket_path_.c_str(), sizeof(server_addr_.sun_path) - 1);

    connected_.store(true);
    printf("DGRAM client bound to %s and ready for server: %s\n", client_socket_path_.c_str(), socket_path_.c_str());
    return true;
}

void UnixSocketClientDgram::disconnect() {
    if (!connected_.load()) {
        return;
    }

    // 调用基类的disconnect方法
    UnixSocketClient::disconnect();

    // 删除客户端socket文件
    if (!client_socket_path_.empty()) {
        unlink(client_socket_path_.c_str());
        printf("Removed DGRAM client socket file: %s\n", client_socket_path_.c_str());
    }
}

bool UnixSocketClientDgram::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool inline UnixSocketClientDgram::receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data) {
    struct sockaddr_un from_addr;
    socklen_t from_len = sizeof(from_addr);

    // 接收完整消息（头部+数据）
    ssize_t received = recvfrom(client_fd_, buffer_.data(), buffer_.size(), 0,
                               (struct sockaddr*)&from_addr, &from_len);

    if (received < static_cast<ssize_t>(sizeof(header))) {
        return false;
    }

    // 解析消息头
    memcpy(&header, buffer_.data(), sizeof(header));

    if (header.proto_version != 10) {
        printf("Invalid protocol version in received message\n");
        return false;
    }

    if (header.msg_len > MAX_MESSAGE_SIZE - sizeof(header)) {
        printf("Message too large: %u\n", header.msg_len);
        return false;
    }

    // 验证接收到的数据长度
    if (received != static_cast<ssize_t>(sizeof(header) + header.msg_len)) {
        printf("Incomplete message received\n");
        return false;
    }

    // 解析消息体
    if (header.msg_len > 0) {
        data.resize(header.msg_len);
        memcpy(data.data(), buffer_.data() + sizeof(header), header.msg_len);
    } else {
        data.clear();
    }

    return true;
}

bool UnixSocketClientDgram::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClientDgram::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClientDgram::send_message_internal(const ipc_msg_hdr& header, const uint8_t* data) {
    // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
    size_t total_size = sizeof(header) + header.msg_len;
    std::vector<uint8_t> message_buffer(total_size);

    // 拷贝消息头
    memcpy(message_buffer.data(), &header, sizeof(header));

    // 拷贝消息体（如果有）
    if (header.msg_len > 0 && data) {
        memcpy(message_buffer.data() + sizeof(header), data, header.msg_len);
    }

    // 一次性发送完整消息
    ssize_t sent = sendto(client_fd_, message_buffer.data(), total_size, 0,
                         (struct sockaddr*)&server_addr_, sizeof(server_addr_));

    return sent == static_cast<ssize_t>(total_size);
}



// ============================================================================
// UnixSocketClientStream 实现（STREAM套接字，用于控制消息）
// ============================================================================

UnixSocketClientStream::UnixSocketClientStream(const std::string& socket_path)
    : UnixSocketClient(socket_path, SocketType::STREAM) {
}

UnixSocketClientStream::~UnixSocketClientStream() {
}

bool UnixSocketClientStream::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (client_fd_ == -1) {
        printf("Failed to create STREAM client socket: %s\n", strerror(errno));
        return false;
    }

    // 设置服务器地址
    struct sockaddr_un server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, socket_path_.c_str(), sizeof(server_addr.sun_path) - 1);

    // 连接到服务器
    if (::connect(client_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) == -1) {
        printf("Failed to connect to STREAM server: %s\n", strerror(errno));
        close(client_fd_);
        client_fd_ = -1;
        return false;
    }

    connected_.store(true);
    printf("STREAM client connected to server: %s\n", socket_path_.c_str());
    return true;
}

bool UnixSocketClientStream::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool UnixSocketClientStream::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClientStream::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClientStream::send_message_internal(const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd_, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体（如果有）
    if (header.msg_len > 0 && data) {
        sent = send(client_fd_, data, header.msg_len, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketClientStream::receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data) {
    // 接收消息头
    ssize_t received = recv(client_fd_, &header, sizeof(header), 0);
    if (received != sizeof(header)) {
        return false;
    }

    if (header.proto_version != 10) {
        printf("Invalid protocol version in received message\n");
        return false;
    }

    if (header.msg_len > MAX_MESSAGE_SIZE) {
        printf("Message too large: %u\n", header.msg_len);
        return false;
    }

    // 接收消息体
    if (header.msg_len > 0) {
        data.resize(header.msg_len);
        received = recv(client_fd_, data.data(), header.msg_len, 0);
        if (received != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    } else {
        data.clear();
    }

    return true;
}
