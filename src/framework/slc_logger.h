
#ifndef __SLC_LOGGER_H_
#define __SLC_LOGGER_H_

#include <string>
#include <arpa/inet.h>
#include <iostream>
#include <sstream>
#include <cstdio>
enum class LogLevel {
  Debug,
  Error,
  Warning,
  Info,
};
namespace slc {

class Logger {
public:
  Logger() {}
  Logger(int level) {
    switch (level) {
      case 0:
        SetLogLevel(LogLevel::Info);
        break;
      case 1:
        SetLogLevel(LogLevel::Warning);
        break;
      case 2:
        SetLogLevel(LogLevel::Error);
        break;
      case 3:
        SetLogLevel(LogLevel::Debug);
        break;
    }
  }

  void SetLogLevel(LogLevel level) { logLevel = level; }
  void SetLogLevel(int level) {
    switch (level) {
      case 0:
        SetLogLevel(LogLevel::Debug);
        break;
      case 1:
        SetLogLevel(LogLevel::Error);
        break;
      case 2:
        SetLogLevel(LogLevel::Warning);
        break;
      case 3:
        SetLogLevel(LogLevel::Info);
        break;
    }
  }

  template <typename... Args>
  void Debug(const std::string &message, Args... args) {
    Log(LogLevel::Debug, message, args...);
  }

  template <typename... Args>
  void Info(const std::string &message, Args... args) {
    Log(LogLevel::Info, message, args...);
  }

  template <typename... Args>
  void Warning(const std::string &message, Args... args) {
    Log(LogLevel::Warning, message, args...);
  }

  template <typename... Args>
  void Error(const std::string &message, Args... args) {
    Log(LogLevel::Error, message, args...);
  }

private:
  LogLevel logLevel;
  template <typename... Args>
  void Log(LogLevel level, const std::string &message, Args... args) {
    if (level >= logLevel) {
      std::string levelStr;
      switch (level) {
        case LogLevel::Debug:
          levelStr = "DEBUG";
          break;
        case LogLevel::Info:
          levelStr = "INFO";
          break;
        case LogLevel::Warning:
          levelStr = "WARNING";
          break;
        case LogLevel::Error:
          levelStr = "ERROR";
          break;
      }

      std::ostringstream formattedMessage;
      (formattedMessage << ... << args);  // Using fold expression to format args
      printf("[%s] %s: %s\n", levelStr.c_str(), message.c_str(), formattedMessage.str().c_str());
    }
  }
};

}  // namespace slc
#endif