SdpInfo::SetRtspinfo (this=0x6320000191c0, flow=0x632000018810, cli_port=0x63200002da1a "52258-52259", srv_port=0x63200002da26 "10080-10081") 
 p  info.m_info
$20 = {{m_media = "video 0 RTP/AVP 105", '\000' <repeats 236 times>, m_type = "application", '\000' <repeats 52 times>, m_port = "0", '\000' <repeats 62 times>,
    m_proto = "RTP/AVP", '\000' <repeats 56 times>, m_title = '\000' <repeats 63 times>, m_payloads = "DynamicRTP-Type-107;", '\000' <repeats 107 times>, a_attr_num = 4, a_attributes = {
      "control:rtsp://192.168.1.13/media/video1/video", '\000' <repeats 209 times>, "rtpmap:105 H264/90000", '\000' <repeats 234 times>,
      "fmtp:105 profile-level-id=64001f; packetization-mode=1; sprop-parameter-sets=Z2QAH6w7UCgC3QgAAB9AAAYahCA=,aOqPLA==", '\000' <repeats 141 times>, "recvonly", '\000' <repeats 247 times>,
      '\000' <repeats 255 times> <repeats 36 times>}}, {m_media = "audio 0 RTP/AVP 0", '\000' <repeats 238 times>, m_type = '\000' <repeats 63 times>, m_port = '\000' <repeats 63 times>,
    m_proto = '\000' <repeats 63 times>, m_title = '\000' <repeats 63 times>, m_payloads = '\000' <repeats 127 times>, a_attr_num = 3, a_attributes = {"fmtp:0 RTCP=0", '\000' <repeats 242 times>,
      "control:rtsp://192.168.1.13/media/video1/audio1", '\000' <repeats 208 times>, "recvonly", '\000' <repeats 247 times>, '\000' <repeats 255 times> <repeats 37 times>}}, {
    m_media = "application 0 RTP/AVP 107", '\000' <repeats 230 times>, m_type = '\000' <repeats 63 times>, m_port = '\000' <repeats 63 times>, m_proto = '\000' <repeats 63 times>,
    m_title = '\000' <repeats 63 times>, m_payloads = '\000' <repeats 127 times>, a_attr_num = 4, a_attributes = {"control:rtsp://192.168.1.13/media/video1/metadata", '\000' <repeats 206 times>,
      "rtpmap:107 vnd.onvif.metadata/90000", '\000' <repeats 220 times>, "fmtp:107 DecoderTag=h3c-v3 RTCP=0", '\000' <repeats 222 times>, "recvonly", '\000' <repeats 247 times>,
      '\000' <repeats 255 times> <repeats 36 times>}}, {m_media = '\000' <repeats 255 times>, m_type = '\000' <repeats 63 times>, m_port = '\000' <repeats 63 times>,
    m_proto = '\000' <repeats 63 times>, m_title = '\000' <repeats 63 times>, m_payloads = '\000' <repeats 127 times>, a_attr_num = 0, a_attributes = {'\000' <repeats 255 times> <repeats 40 times>}},
  {m_media = '\000' <repeats 255 times>, m_type = '\000' <repeats 63 times>, m_port = '\000' <repeats 63 times>, m_proto = '\000' <repeats 63 times>, m_title = '\000' <repeats 63 times>,
    m_payloads = '\000' <repeats 127 times>, a_attr_num = 0, a_attributes = {'\000' <repeats 255 times> <repeats 40 times>}}, {m_media = '\000' <repeats 255 times>,
    m_type = '\000' <repeats 63 times>, m_port = '\000' <repeats 63 times>, m_proto = '\000' <repeats 63 times>, m_title = '\000' <repeats 63 times>, m_payloads = '\000' <repeats 127 times>,
    a_attr_num = 0, a_attributes = {'\000' <repeats 255 times> <repeats 40 times>}}}

p sipMeidaDescription_v
$22 = std::set with 3 elements = {[0] = "application 0 RTP/AVP 107", [1] = "audio 0 RTP/AVP 0", [2] = "video 0 RTP/AVP 105"}
