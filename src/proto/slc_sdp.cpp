#include "slc_sdp.h"
#include "ipc/ipc_decoder.h"
#include "slc_common.h"
#include "slc_rtp.h"
#include <vector>
#include <string>
#include <sstream>
#include <stdexcept>

#include "common/c_lang_linkage_start.h"

/****************************************************************************************
 * 文 件 名 : dpi_sdp.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/18
编码: wangy            2018/07/18
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include "glib-2.0/glib.h"
#include <string.h>

static const value_string rtp_payload_type_vals[] = {
    /*  0 */ {PT_PCMU, "ITU-T G.711 PCMU"},
    /*  1 */ {PT_1016, "USA Federal Standard FS-1016"},
    /*  2 */ {PT_G721, "ITU-T G.721"},
    /*  3 */ {PT_GSM, "GSM 06.10"},
    /*  4 */ {PT_G723, "ITU-T G.723"},
    /*  5 */ {PT_DVI4_8000, "DVI4 8000 samples/s"},
    /*  6 */ {PT_DVI4_16000, "DVI4 16000 samples/s"},
    /*  7 */ {PT_LPC, "Experimental linear predictive encoding from Xerox PARC"},
    /*  8 */ {PT_PCMA, "ITU-T G.711 PCMA"},
    /*  9 */ {PT_G722, "ITU-T G.722"},
    /* 10 */ {PT_L16_STEREO, "16-bit uncompressed audio, stereo"},
    /* 11 */ {PT_L16_MONO, "16-bit uncompressed audio, monaural"},
    /* 12 */ {PT_QCELP, "Qualcomm Code Excited Linear Predictive coding"},
    /* 13 */ {PT_CN, "Comfort noise"},
    /* 14 */ {PT_MPA, "MPEG-I/II Audio"},
    /* 15 */ {PT_G728, "ITU-T G.728"},
    /* 16 */ {PT_DVI4_11025, "DVI4 11025 samples/s"},
    /* 17 */ {PT_DVI4_22050, "DVI4 22050 samples/s"},
    /* 18 */ {PT_G729, "ITU-T G.729"},
    /* 19 */ {PT_CN_OLD, "Comfort noise (old)"},
    /* 20 */ {20, "Unassigned"},
    /* 21 */ {21, "Unassigned"},
    /* 22 */ {22, "Unassigned"},
    /* 23 */ {23, "Unassigned"},
    /* 24 */ {24, "Unassigned"},
    /* 25 */ {PT_CELB, "Sun CellB video encoding"},
    /* 26 */ {PT_JPEG, "JPEG-compressed video"},
    /* 27 */ {27, "Unassigned"},
    /* 28 */ {PT_NV, "'nv' program"},
    /* 29 */ {29, "Unassigned"},
    /* 30 */ {30, "Unassigned"},
    /* 31 */ {PT_H261, "ITU-T H.261"},
    /* 32 */ {PT_MPV, "MPEG-I/II Video"},
    /* 33 */ {PT_MP2T, "MPEG-II transport streams"},
    /* 34 */ {PT_H263, "ITU-T H.263"},
    /* 35-71     Unassigned  */
    /* 35 */ {35, "Unassigned"},
    /* 36 */ {36, "Unassigned"},
    /* 37 */ {37, "Unassigned"},
    /* 38 */ {38, "Unassigned"},
    /* 39 */ {39, "Unassigned"},
    /* 40 */ {40, "Unassigned"},
    /* 41 */ {41, "Unassigned"},
    /* 42 */ {42, "Unassigned"},
    /* 43 */ {43, "Unassigned"},
    /* 44 */ {44, "Unassigned"},
    /* 45 */ {45, "Unassigned"},
    /* 46 */ {46, "Unassigned"},
    /* 47 */ {47, "Unassigned"},
    /* 48 */ {48, "Unassigned"},
    /* 49 */ {49, "Unassigned"},
    /* 50 */ {50, "Unassigned"},
    /* 51 */ {51, "Unassigned"},
    /* 52 */ {52, "Unassigned"},
    /* 53 */ {53, "Unassigned"},
    /* 54 */ {54, "Unassigned"},
    /* 55 */ {55, "Unassigned"},
    /* 56 */ {56, "Unassigned"},
    /* 57 */ {57, "Unassigned"},
    /* 58 */ {58, "Unassigned"},
    /* 59 */ {59, "Unassigned"},
    /* 60 */ {60, "Unassigned"},
    /* 61 */ {61, "Unassigned"},
    /* 62 */ {62, "Unassigned"},
    /* 63 */ {63, "Unassigned"},
    /* 64 */ {64, "Unassigned"},
    /* 65 */ {65, "Unassigned"},
    /* 66 */ {66, "Unassigned"},
    /* 67 */ {67, "Unassigned"},
    /* 68 */ {68, "Unassigned"},
    /* 69 */ {69, "Unassigned"},
    /* 70 */ {70, "Unassigned"},
    /* 71 */ {71, "Unassigned"},
    /* 72-76     Reserved for RTCP conflict avoidance                                  [RFC3551] */
    /* 72 */ {72, "Reserved for RTCP conflict avoidance"},
    /* 73 */ {73, "Reserved for RTCP conflict avoidance"},
    /* 74 */ {74, "Reserved for RTCP conflict avoidance"},
    /* 75 */ {75, "Reserved for RTCP conflict avoidance"},
    /* 76 */ {76, "Reserved for RTCP conflict avoidance"},
    /* 77-95     Unassigned      ? */
    /* 77 */ {77, "Unassigned"},
    /* 78 */ {78, "Unassigned"},
    /* 79 */ {79, "Unassigned"},
    /* 80 */ {80, "Unassigned"},
    /* 81 */ {81, "Unassigned"},
    /* 82 */ {82, "Unassigned"},
    /* 83 */ {83, "Unassigned"},
    /* 84 */ {84, "Unassigned"},
    /* 85 */ {85, "Unassigned"},
    /* 86 */ {86, "Unassigned"},
    /* 87 */ {87, "Unassigned"},
    /* 88 */ {88, "Unassigned"},
    /* 89 */ {89, "Unassigned"},
    /* 90 */ {90, "Unassigned"},
    /* 91 */ {91, "Unassigned"},
    /* 92 */ {92, "Unassigned"},
    /* 93 */ {93, "Unassigned"},
    /* 94 */ {94, "Unassigned"},
    /* 95 */ {95, "Unassigned"},
    /* Added to support addtional RTP payload types
         * See epan/rtp_pt.h */
    {PT_UNDF_96, "DynamicRTP-Type-96"},
    {PT_UNDF_97, "DynamicRTP-Type-97"},
    {PT_UNDF_98, "DynamicRTP-Type-98"},
    {PT_UNDF_99, "DynamicRTP-Type-99"},
    {PT_UNDF_100, "DynamicRTP-Type-100"},
    {PT_UNDF_101, "DynamicRTP-Type-101"},
    {PT_UNDF_102, "DynamicRTP-Type-102"},
    {PT_UNDF_103, "DynamicRTP-Type-103"},
    {PT_UNDF_104, "DynamicRTP-Type-104"},
    {PT_UNDF_105, "DynamicRTP-Type-105"},
    {PT_UNDF_106, "DynamicRTP-Type-106"},
    {PT_UNDF_107, "DynamicRTP-Type-107"},
    {PT_UNDF_108, "DynamicRTP-Type-108"},
    {PT_UNDF_109, "DynamicRTP-Type-109"},
    {PT_UNDF_110, "DynamicRTP-Type-110"},
    {PT_UNDF_111, "DynamicRTP-Type-111"},
    {PT_UNDF_112, "DynamicRTP-Type-112"},
    {PT_UNDF_113, "DynamicRTP-Type-113"},
    {PT_UNDF_114, "DynamicRTP-Type-114"},
    {PT_UNDF_115, "DynamicRTP-Type-115"},
    {PT_UNDF_116, "DynamicRTP-Type-116"},
    {PT_UNDF_117, "DynamicRTP-Type-117"},
    {PT_UNDF_118, "DynamicRTP-Type-118"},
    {PT_UNDF_119, "DynamicRTP-Type-119"},
    {PT_UNDF_120, "DynamicRTP-Type-120"},
    {PT_UNDF_121, "DynamicRTP-Type-121"},
    {PT_UNDF_122, "DynamicRTP-Type-122"},
    {PT_UNDF_123, "DynamicRTP-Type-123"},
    {PT_UNDF_124, "DynamicRTP-Type-124"},
    {PT_UNDF_125, "DynamicRTP-Type-125"},
    {PT_UNDF_126, "DynamicRTP-Type-126"},
    {PT_UNDF_127, "DynamicRTP-Type-127"},

    {0, NULL},
};
#include "common/c_lang_linkage_end.h"

// 安全的字符串分割函数 - 使用简单的C风格实现避免C++标准库问题
static void safeSplitString(const char* str, char delimiter, char tokens[][256], int* token_count, int max_tokens) {
  *token_count = 0;
  if (!str || !*str || max_tokens <= 0) {
    return;
  }

  int str_len = strlen(str);
  if (str_len > 1024) {  // 防止过长输入
    return;
  }

  int start = 0;
  int token_len = 0;

  for (int i = 0; i <= str_len && *token_count < max_tokens; i++) {
    if (str[i] == delimiter || str[i] == '\0') {
      if (token_len > 0) {
        // 跳过前导空格
        while (start < i && (str[start] == ' ' || str[start] == '\t')) {
          start++;
          token_len--;
        }
        // 跳过尾随空格
        int end = start + token_len - 1;
        while (end >= start && (str[end] == ' ' || str[end] == '\t')) {
          end--;
          token_len--;
        }

        if (token_len > 0 && token_len < 255) {
          strncpy(tokens[*token_count], str + start, token_len);
          tokens[*token_count][token_len] = '\0';
          (*token_count)++;
        }
      }
      start = i + 1;
      token_len = 0;
    } else {
      token_len++;
    }
  }
}

// 安全的字符串转整数函数
static bool safeStringToInt(const char* str, int* result) {
  if (!str || !result || strlen(str) == 0 || strlen(str) > 14) {
    return false;
  }

  char* endptr;
  long val = strtol(str, &endptr, 10);

  // 检查是否整个字符串都被转换，且在int范围内
  if ((*endptr == '\0' || *endptr == '-') && val >= INT_MIN && val <= INT_MAX) {
    *result = (int)val;
    return true;
  }

  return false;
}

void SdpInfo::_copy_one_item(char *result, int max_len, const uint8_t *value, uint16_t len) {
  int copy_len;
  copy_len = len;
  if (copy_len > max_len - 1)
    copy_len = max_len - 1;

  strncpy(result, (const char *)value, copy_len);
  result[copy_len] = 0;

  return;
}

void SdpInfo::_add_one_item_to_hash(GHashTable *table, const char *header, const uint8_t *value, uint16_t len) {
  char                *_header;
  struct header_value *_value;
  _value = (struct header_value *)malloc(sizeof(struct header_value));
  if (!_value)
    return;
  _header = strdup(header);
  if (!_header) {
    free(_value);
    return;
  }
  _value->need_free = 0;
  _value->len = len;
  _value->ptr = value;
  g_hash_table_insert(table, _header, _value);

  return;
}

void SdpInfo::dissect_sdp_owner(const uint8_t *line, int line_len, GHashTable *table) {
  int                  black_num = 0;
  int                  black_index;
  int                  index = 0;
  struct header_value *_value;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    _value = (struct header_value *)malloc(sizeof(struct header_value));
    if (!_value)
      return;
    _value->need_free = 0;
    _value->ptr = line + index;
    _value->len = black_index;

    switch (black_num) {
      case 0:
        g_hash_table_insert(table, strdup(SDP_O_USERNAME), _value);
        break;
      case 1:
        g_hash_table_insert(table, strdup(SDP_O_SESSIONID), _value);
        break;
      case 2:
        g_hash_table_insert(table, strdup(SDP_O_VERSION), _value);
        break;
      case 3:
        g_hash_table_insert(table, strdup(SDP_O_NETWORK_TYPE), _value);
        break;
      case 4:
        g_hash_table_insert(table, strdup(SDP_O_ADDRESS_TYPE), _value);

        if (line_len > index + black_index + 1) {
          _value = (struct header_value *)malloc(sizeof(struct header_value));
          if (!_value)
            return;
          _value->need_free = 0;
          _value->ptr = line + index + black_index + 1;
          _value->len = line_len - index - black_index - 1;
          g_hash_table_insert(table, strdup(SDP_O_ADDRESS), _value);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

void SdpInfo::dissect_sdp_connnection_info(const uint8_t *line, int line_len, GHashTable *table) {
  int                  black_num = 0;
  int                  black_index;
  int                  index = 0;
  struct header_value *_value;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    _value = (struct header_value *)malloc(sizeof(struct header_value));
    if (!_value)
      return;
    _value->need_free = 0;
    _value->ptr = line + index;
    _value->len = black_index;

    switch (black_num) {
      case 0:
        g_hash_table_insert(table, strdup(SDP_C_NETWORK_TYPE), _value);
        break;
      case 1:
        g_hash_table_insert(table, strdup(SDP_C_ADDRESS_TYPE), _value);

        if (line_len > index + black_index + 1) {
          _value = (struct header_value *)malloc(sizeof(struct header_value));
          if (!_value)
            return;
          _value->need_free = 0;
          _value->ptr = line + index + black_index + 1;
          _value->len = line_len - index - black_index - 1;
          g_hash_table_insert(table, strdup(SDP_C_ADDRESS), _value);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

void SdpInfo::dissect_sdp_time(const uint8_t *line, int line_len, GHashTable *table) {
  int                  black_index;
  struct header_value *_value;

  black_index = find_blank_space(line, line_len);
  if (black_index <= 0) {
    return;
  }
  _value = (struct header_value *)malloc(sizeof(struct header_value));
  if (!_value)
    return;
  _value->need_free = 0;
  _value->ptr = line;
  _value->len = black_index;

  g_hash_table_insert(table, strdup(SDP_T_TIME_START), _value);

  if (line_len > black_index + 1) {
    _value = (struct header_value *)malloc(sizeof(struct header_value));
    if (!_value)
      return;
    _value->need_free = 0;
    _value->ptr = line + black_index + 1;
    _value->len = line_len - black_index - 1;
    g_hash_table_insert(table, strdup(SDP_T_TIME_END), _value);
  }

  return;
}

void SdpInfo::find_media_payload(const uint8_t *start, int len, char *result, uint16_t *len_ptr) {
  int  black_index;
  int  offset = 0;
  char num_str[32];
  int  copy_len;
  int  num;
  int  _len = 0;

  while (offset < len) {
    black_index = find_blank_space(start + offset, len - offset);
    if (black_index <= 0) {
      break;
    }
    copy_len = black_index >= 32 ? 31 : black_index;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, MEDIA_PAYLOAD_LEN_MAX - _len, "%s;", rtp_payload_type_vals[num].strptr);
    }
    offset += black_index + 1;
  }

  if (offset < len) {
    copy_len = len - offset >= 32 ? 31 : len - offset;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, MEDIA_PAYLOAD_LEN_MAX - _len, "%s;", rtp_payload_type_vals[num].strptr);
    }
  }
  *len_ptr = _len;
}

void SdpInfo::dissect_sdp_media(const uint8_t *line, int line_len, GHashTable *table) {
  int                      black_num = 0;
  int                      black_index;
  int                      index = 0;
  struct header_value     *_value;
  struct header_tmp_value *_tmp_value;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    _value = (struct header_value *)malloc(sizeof(struct header_value));
    if (!_value)
      return;
    _value->need_free = 0;
    _value->ptr = line + index;
    _value->len = black_index;

    switch (black_num) {
      case 0:
        g_hash_table_insert(table, strdup(SDP_M_TYPE), _value);
        break;
      case 1:
        g_hash_table_insert(table, strdup(SDP_M_PORT), _value);
        break;
      case 2:
        g_hash_table_insert(table, strdup(SDP_M_PROTO), _value);
        if (line_len > index + black_index + 1) {
          _tmp_value = (struct header_tmp_value *)malloc(sizeof(struct header_tmp_value));
          if (!_tmp_value)
            return;

          _tmp_value->need_free = 1;
          _tmp_value->ptr = (uint8_t *)malloc(MEDIA_PAYLOAD_LEN_MAX);
          if (!_tmp_value->ptr) {
            free(_tmp_value);
            return;
          }
          find_media_payload(
              line + index + black_index + 1, line_len - index - black_index - 1, (char *)_tmp_value->ptr, &_tmp_value->len);
          g_hash_table_insert(table, strdup(SDP_M_PAYLOADS), _tmp_value);
        }

        return;  //return
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

void SdpInfo::dissect_sdp_time2(const uint8_t *line, int line_len) {
  int black_index;

  black_index = find_blank_space(line, line_len);
  if (black_index <= 0) {
    return;
  }

  _copy_one_item(info.t_time_start, sizeof(info.t_time_start), line, black_index);

  if (line_len > black_index + 1) {
    _copy_one_item(info.t_time_end, sizeof(info.t_time_end), line + black_index + 1, line_len - black_index - 1);
  }

  return;
}

void SdpInfo::dissect_sdp_connnection_info2(const uint8_t *line, int line_len) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        _copy_one_item(info.c_network_type, sizeof(info.c_network_type), line + index, black_index);
        break;
      case 1:
        _copy_one_item(info.c_address_type, sizeof(info.c_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          _copy_one_item(
              info.c_address, sizeof(info.c_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }
  return;
}

void SdpInfo::dissect_sdp_owner2(const uint8_t *line, int line_len) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    switch (black_num) {
      case 0:
        _copy_one_item(info.o_username, sizeof(info.o_username), line + index, black_index);
        break;
      case 1:
        _copy_one_item(info.o_sessionid, sizeof(info.o_sessionid), line + index, black_index);
        break;
      case 2:
        _copy_one_item(info.o_version, sizeof(info.o_version), line + index, black_index);
        break;
      case 3:
        _copy_one_item(info.o_network_type, sizeof(info.o_network_type), line + index, black_index);
        break;
      case 4:
        _copy_one_item(info.o_address_type, sizeof(info.o_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          _copy_one_item(
              info.o_address, sizeof(info.o_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

void SdpInfo::find_media_payload2(const uint8_t *start, int len, char *result, int max_len) {
  int  black_index;
  int  offset = 0;
  char num_str[32];
  int  copy_len;
  int  num;
  int  _len = 0;

  while (offset < len) {
    black_index = find_blank_space(start + offset, len - offset);
    if (black_index <= 0) {
      break;
    }
    copy_len = black_index >= 32 ? 31 : black_index;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtp_payload_type_vals[num].strptr);
    }
    offset += black_index + 1;
  }

  if (offset < len) {
    copy_len = len - offset >= 32 ? 31 : len - offset;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtp_payload_type_vals[num].strptr);
    }
  }
}

void SdpInfo::dissect_sdp_media2(const uint8_t *line, int line_len, int media_index) {
  int black_num = 0;
  int black_index;
  int index = 0;

  // 边界检查：确保media_index在有效范围内
  if (media_index < 0 || media_index >= SDP_MEDIA_MAX_NUM) {
    return;
  }

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        _copy_one_item(info.m_info[media_index].m_type, sizeof(info.m_info[media_index].m_type), line + index, black_index);
        break;
      case 1:
        _copy_one_item(info.m_info[media_index].m_port, sizeof(info.m_info[media_index].m_port), line + index, black_index);
        break;
      case 2:
        _copy_one_item(info.m_info[media_index].m_proto, sizeof(info.m_info[media_index].m_proto), line + index, black_index);
        if (line_len > index + black_index + 1) {
          find_media_payload2(line + index + black_index + 1, line_len - index - black_index - 1, info.m_info[media_index].m_payloads,
              sizeof(info.m_info[media_index].m_payloads));
        }

        return;  //return
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

int SdpInfo::dissect_sdp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len) {
  uint8_t        f_in_media = 0;
  uint32_t       offset = 0;
  const uint8_t *line;
  int            line_len;
  char           type;
  char           delim;

  int attr_index = 0;
  int media_index = -1;
  memset(&info, 0, sizeof(info));
  line = payload;
  while (offset < payload_len) {
    if (media_index >= SDP_MEDIA_MAX_NUM - 1)
      break;
    line_len = find_packet_line_end(line, payload_len - offset);

    /*
        * Line must contain at least e.g. "v=".
        */
    if (line_len < 2)
      break;

    type = *line;
    delim = *(line + 1);
    if (delim != '=')
      goto next_line;

    switch (type) {
      case 'v':
        _copy_one_item(info.v_version, sizeof(info.v_version), line + 2, line_len - 2);
        break;
      case 'o':
        _copy_one_item(info.o_owner, sizeof(info.o_owner), line + 2, line_len - 2);
        dissect_sdp_owner2(line + 2, line_len - 2);
        break;
      case 's':
        _copy_one_item(info.s_name, sizeof(info.s_name), line + 2, line_len - 2);
        break;
      case 'i':
        if (f_in_media)
          _copy_one_item(info.m_info[media_index].m_title, sizeof(info.m_info[media_index].m_title), line + 2, line_len - 2);
        else
          _copy_one_item(info.i_info, sizeof(info.i_info), line + 2, line_len - 2);
        break;
      case 'u':
        _copy_one_item(info.u_uri, sizeof(info.u_uri), line + 2, line_len - 2);
        break;
      case 'e':
        _copy_one_item(info.e_email, sizeof(info.e_email), line + 2, line_len - 2);
        break;
      case 'p':
        _copy_one_item(info.p_phone, sizeof(info.p_phone), line + 2, line_len - 2);
        break;
      case 'c':
        _copy_one_item(info.c_info, sizeof(info.c_info), line + 2, line_len - 2);
        dissect_sdp_connnection_info2(line + 2, line_len - 2);
        break;
      case 'b':
        _copy_one_item(info.b_bandwidths, sizeof(info.b_bandwidths), line + 2, line_len - 2);
        break;
      case 't':
        _copy_one_item(info.t_time, sizeof(info.t_time), line + 2, line_len - 2);
        dissect_sdp_time2(line + 2, line_len - 2);
        break;
      case 'r':
        _copy_one_item(info.r_repeattime, sizeof(info.r_repeattime), line + 2, line_len - 2);
        break;
      case 'm':
        f_in_media = 1;
        media_index++;
        info.m_info_num++;
        attr_index = 0;
        _copy_one_item(info.m_info[media_index].m_media, sizeof(info.m_info[media_index].m_media), line + 2, line_len - 2);
        dissect_sdp_media2(line + 2, line_len - 2, media_index);
        break;

      //now only one attributes is writing
      case 'a':
        if (f_in_media) {
          if (attr_index >= SDP_M_ATTR_MAX_NUM)
            break;
          _copy_one_item(info.m_info[media_index].a_attributes[attr_index],
              sizeof(info.m_info[media_index].a_attributes[attr_index]), line + 2, line_len - 2);
          attr_index++;
          info.m_info[media_index].a_attr_num++;
        } else
          _copy_one_item(info.session_attribute, sizeof(info.session_attribute), line + 2, line_len - 2);
        break;
      default:
        break;
    }
  next_line:
    offset += line_len + 2;
    line = &payload[offset];
  }
  dissectSipSDP(flow);
  return 0;
}

// 辅助函数：解析媒体描述字符串
bool SdpInfo::parseMediaDescription(const std::string& description, MediaDescriptionInfo& info) {
  // 输入验证
  if (description.empty() || description.length() > 1024) {
    return false;
  }

  // 使用安全的字符串分割方法
  char tokens[20][256];  // 最多20个token，每个最长255字符
  int token_count = 0;
  safeSplitString(description.c_str(), ' ', tokens, &token_count, 20);

  // 验证最少需要的字段数量
  if (token_count < 3) {
    return false;
  }

  // 解析基本字段
  info.mediaType = std::string(tokens[0]);  // 媒体类型 audio; video

  // 安全地解析端口
  int port_int = 0;
  // 0可能是rtsp的端口
  if (!safeStringToInt(tokens[1], &port_int) || port_int < 0 || port_int > 65535) {
    return false;
  }
  info.port = (uint32_t)port_int;

  info.transportType = std::string(tokens[2]);  // 传输协议

  // 收集媒体属性（从第4个字段开始）
  info.media_attr_count = 0;
  for (int j = 3; j < token_count && info.media_attr_count < 16; j++) {
    if (strlen(tokens[j]) > 0) {
      info.media_attr[info.media_attr_count++] = tokens[j];
    }
  }

  return true;
}

// 辅助函数：处理端口映射逻辑
bool SdpInfo::processPortMapping(struct flow_info* flow, const std::string& mediaType, uint32_t port,
                                RtpPortInfo& rtpPort, bool& shouldContinue) {
  shouldContinue = false;

  // 建立 sip INVITE 与 200OK 的conv
  // 正反向ip
  auto tuple = std::make_pair(server_ip_, cli_ip_);
  auto tuple_resv = std::make_pair(cli_ip_, server_ip_);
  // 区分音视频
  auto key = std::make_pair(tuple, mediaType);
  auto key_resv = std::make_pair(tuple_resv, mediaType);

  // 验证RTPKEEPER指针
  if (!RTPKEEPER) {
    shouldContinue = true;
    return false;
  }

  auto mapFindIter = RTPKEEPER->map_serverIp2rtpPortInfo_.find(key);
  if (mapFindIter == RTPKEEPER->map_serverIp2rtpPortInfo_.end()) {
    mapFindIter = RTPKEEPER->map_serverIp2rtpPortInfo_.find(key_resv);
  }

  if (mapFindIter == RTPKEEPER->map_serverIp2rtpPortInfo_.end()) {
    // 第一次遇到此媒体类型（通常是INVITE请求）
    rtpPort.port_src = port;

    RTPKEEPER->map_serverIp2rtpPortInfo_.emplace(key, rtpPort);
    RTPKEEPER->map_serverIp2rtpPortInfo_.emplace(key_resv, rtpPort);

    // 对于请求和响应端口相同的情况，需要继续处理媒体属性
    // 检查是否是200 OK响应，如果是则继续处理
    if (sip_status_ == "200 OK") {
      // 设置端口信息用于后续处理
      rtpPort.port_dst = port;
      rtp_src_port = port;
      rtp_dst_port = port;
      // 不要continue，继续处理媒体属性
      return true;
    } else {
      shouldContinue = true;  // 非200 OK，跳过媒体属性处理
      return false;
    }
  } else {
    // 第二次遇到此媒体类型（通常是200 OK响应）
    rtpPort.port_src = mapFindIter->second.port_src;
    rtpPort.port_dst = port;
    rtp_src_port = rtpPort.port_src;
    rtp_dst_port = port;
    // 移除已经处理过的conv
    RTPKEEPER->map_serverIp2rtpPortInfo_.erase(key);
    RTPKEEPER->map_serverIp2rtpPortInfo_.erase(key_resv);
    return true;
  }
}

// 辅助函数：处理媒体属性和payload类型
void SdpInfo::processMediaAttributes(const MediaDescriptionInfo& mediaInfo, RtpPortInfo& rtpPort,
                                    RtpMediaInfo& minfo, struct flow_info* flow) {
  // 处理媒体属性中的payload类型
  for (int attr_idx = 0; attr_idx < mediaInfo.media_attr_count; attr_idx++) {
    const char* attr_str = mediaInfo.media_attr[attr_idx];
    if (strlen(attr_str) > 0) {
      int payload_type = 0;
      // 使用安全的字符串转换
      if (!safeStringToInt(attr_str, &payload_type)) {
        continue;  // 转换失败，跳过此属性
      }

      // 验证payload_type在有效范围内
      if (payload_type < 0 || payload_type > PT_UNDF_127) {
        continue;
      }

      minfo.rtpPayloadType = payload_type;
      rtpPort.payload_type = payload_type;

      // 对于静态payload类型（0-34），直接创建映射
      if (payload_type <= PT_H263) {
        createMediaMapping(rtpPort, minfo, flow);
      }
      // 对于动态payload类型（96-127），需要通过rtpmap确定媒体类型
      // 继续处理rtpmap属性
    }
  }
}

// 辅助函数：创建媒体映射
void SdpInfo::createMediaMapping(RtpPortInfo& rtpPort, RtpMediaInfo& minfo, struct flow_info* flow) {
  RtpPortInfo rtpPortResv;
  rtpPortResv.port_dst = rtpPort.port_src;
  rtpPortResv.port_src = rtpPort.port_dst;
  RTPKEEPER->map_port2rtpMediaInfo_.emplace(rtpPort, minfo);
  conversion_flag_ = 1;
}

// 辅助函数：处理rtpmap解析和媒体类型识别
void SdpInfo::processRtpMap(const char* media_attr_str, RtpMediaInfo& minfo, RtpPortInfo& rtpPort,
                           struct flow_info* flow, int media_index) {
  // 检查字符串长度，防止过长输入
  size_t attr_len = strlen(media_attr_str);
  if (attr_len > 512) {
    return;
  }

  // 安全处理ssrc属性
  if (strstr(media_attr_str, "ssrc:") != NULL) {
    if (attr_len > 5) {
      const char* ssrc_str = media_attr_str + 5;
      int ssrc_val = 0;
      if (safeStringToInt(ssrc_str, &ssrc_val) && ssrc_val >= 0) {
        ssrc = (uint32_t)ssrc_val;
      }
    }
  }

  if (strstr(media_attr_str, "rtpmap") == NULL) {
    return;
  }

  // 安全地解析媒体格式类型
  const char* space_ptr = strchr(media_attr_str, ' ');
  const char* slash_ptr = strchr(media_attr_str, '/');

  if (!space_ptr || space_ptr + 1 >= media_attr_str + attr_len) {
    return;
  }

  // 提取媒体格式类型
  char mediaFormatType_type[128] = {0};
  const char* start = space_ptr + 1;
  size_t copy_len;

  if (slash_ptr && slash_ptr > start) {
    copy_len = slash_ptr - start;
  } else {
    copy_len = strlen(start);
  }

  if (copy_len >= sizeof(mediaFormatType_type)) {
    copy_len = sizeof(mediaFormatType_type) - 1;
  }

  strncpy(mediaFormatType_type, start, copy_len);
  mediaFormatType_type[copy_len] = '\0';

  // 媒体类型识别
  if (strstr(mediaFormatType_type, "H264") != NULL) {
    minfo.mediaType = RtpMediaType::video_h264;
    // 安全地解析采样率
    if (slash_ptr && slash_ptr + 1 < media_attr_str + attr_len) {
      const char* rate_str = slash_ptr + 1;
      int rate_val = 0;
      if (safeStringToInt(rate_str, &rate_val) && rate_val > 0) {
        sample_rate_ = rate_val;
      }
    }
  } else if (strstr(mediaFormatType_type, "H263") != NULL) {
    minfo.mediaType = RtpMediaType::video_h263;
  } else if (strstr(mediaFormatType_type, "H261") != NULL) {
    minfo.mediaType = RtpMediaType::video_h261;
  } else if (strstr(mediaFormatType_type, "opus") != NULL ||
             strstr(mediaFormatType_type, "OPUS") != NULL ||
             strstr(mediaFormatType_type, "G729") != NULL ||
             strstr(mediaFormatType_type, "G726") != NULL ||
             strstr(mediaFormatType_type, "SIREN") != NULL) {
    minfo.mediaType = RtpMediaType::audio;
  } else if (strstr(mediaFormatType_type, "AMR") != NULL ||
             strstr(mediaFormatType_type, "723") != NULL ||
             strstr(mediaFormatType_type, "728") != NULL) {
    minfo.mediaType = RtpMediaType::raw;
  }

  // 确保为所有识别的媒体类型创建映射，包括动态payload类型
  createMediaMapping(rtpPort, minfo, flow);
}

//解析 media description
//例子 audio 4000 RTP/AVP 0 101
//例子 video 4002 RTP/AVP 99
void SdpInfo::dissectSipMeidaDescription(struct flow_info *flow) {
  // 输入验证
  if (!flow) {
    return;
  }

  // 验证关键成员变量
  if (sipMeidaDescription_v.empty()) {
    return;
  }

  // 验证info结构体的有效性
  if (info.m_info_num <= 0 || info.m_info_num > SDP_MEDIA_MAX_NUM) {
    return;
  }

  // 每一行 media description解析一次
  int i = -1;
  int processed_count = 0;  // 添加处理计数器，防止无限循环
  const int MAX_PROCESS_COUNT = SDP_MEDIA_MAX_NUM * 2;  // 设置最大处理数量

  for (const auto& sipMeidaDescription : sipMeidaDescription_v) {
    // 防止处理过多条目
    if (++processed_count > MAX_PROCESS_COUNT) {
      break;
    }
    i++;
    if (i >= SDP_MEDIA_MAX_NUM) {
      break;
    }

    // 解析媒体描述
    MediaDescriptionInfo mediaInfo;
    if (!parseMediaDescription(sipMeidaDescription, mediaInfo)) {
      continue;  // 解析失败，跳过此条目
    }

    RtpPortInfo  rtpPort;  //建立 port->rtp的conversion 的 key
    RtpMediaInfo minfo;    //建立 port->rtp的conversion 的 value
    minfo.sip_from = sip_from_;
    minfo.sip_to = sip_to_;
    minfo.sipStream = flow;

    // 验证IP地址不为空
    if (server_ip_.empty() || cli_ip_.empty()) {
      continue;
    }

    // 处理端口映射
    bool shouldContinue = false;
    if (!processPortMapping(flow, mediaInfo.mediaType, mediaInfo.port, rtpPort, shouldContinue)) {
      if (shouldContinue) {
        continue;
      }
    }

    //填充媒体类型 minfo.mediaType 只有当INVITE与200OK都到达时才有意义
    // 默认 200OK后到达
    // TODO:需要考虑 200OK先到达的乱序情况
    // 处理 media_attr 媒体能力
    // 只有在200 OK响应时才处理媒体属性，但不应该阻止其他媒体条目的处理
    if (sip_status_ != "200 OK") {
      continue;  // 跳过当前条目，继续处理下一个媒体条目
    }

    // 边界检查：确保i在有效范围内
    if (i < 0 || i >= SDP_MEDIA_MAX_NUM || i >= info.m_info_num) {
      continue;
    }

    // 200OK 中 默认 media_attr 的第一个值为媒体参数
    int media_attr_rtp_map_num = info.m_info[i].a_attr_num;

    // 验证属性数量在合理范围内
    if (media_attr_rtp_map_num < 0 || media_attr_rtp_map_num > SDP_M_ATTR_MAX_NUM) {
      continue;
    }

    // 处理媒体属性
    processMediaAttributes(mediaInfo, rtpPort, minfo, flow);
    // 处理rtpmap属性
    for (int j = 0; j < media_attr_rtp_map_num; j++) {
      // 边界检查
      if (j >= SDP_M_ATTR_MAX_NUM) {
        break;
      }

      const char* media_attr_str = info.m_info[i].a_attributes[j];

      // 检查当前属性是否包含当前处理的媒体属性
      bool found_attr = false;
      for (int k = 0; k < mediaInfo.media_attr_count; k++) {
        if (strstr(media_attr_str, mediaInfo.media_attr[k]) != NULL) {
          found_attr = true;
          break;
        }
      }
      if (!found_attr) {
        continue;
      }

      processRtpMap(media_attr_str, minfo, rtpPort, flow, i);
    }
    }
  }

void SdpInfo::dissectSipSDP(struct flow_info *flow) {
  //将info中信息处理到c++格式的成员变量中，便于处理

  //如果没有 m: 不解析
  if (info.m_info_num <= 0) {
    memset(&info, 0, sizeof info);
    return;
  }
  for (int i = 0; i < info.m_info_num; i++) {
    sipMeidaDescription_v.emplace(info.m_info[i].m_media);
  }
  dissectSipMeidaDescription(flow);
}

void SdpInfo::SetRtspinfo(flow_info *flow, char *cli_port, char *srv_port) {
  // 输入验证
  if (!flow) {
    return;
  }

  // 验证关键成员变量
  if (sipMeidaDescription_v.empty()) {
    return;
  }

  // 验证info结构体的有效性
  if (info.m_info_num <= 0 || info.m_info_num > SDP_MEDIA_MAX_NUM) {
    return;
  }

  int processed_count = 0;  // 添加处理计数器，防止无限循环
  const int MAX_PROCESS_COUNT = SDP_MEDIA_MAX_NUM * 2;  // 设置最大处理数量
  
  for (const auto& sipMeidaDescription : sipMeidaDescription_v) {
    // 防止处理过多条目
    if (++processed_count > MAX_PROCESS_COUNT) {
      break;
    }

    // 解析媒体描述
    MediaDescriptionInfo mediaInfo;
    if (!parseMediaDescription(sipMeidaDescription, mediaInfo)) {
      continue;  // 解析失败，跳过此条目
    }

    RtpPortInfo  rtpPort;  //建立 port->rtp的conversion 的 key
    RtpMediaInfo minfo;    //建立 port->rtp的conversion 的 value
    minfo.rtspStream = flow;





    // 处理媒体属性中的payload类型
    for (int attr_idx = 0; attr_idx < mediaInfo.media_attr_count; attr_idx++) {
      const char* attr_str = mediaInfo.media_attr[attr_idx];
      if (strlen(attr_str) > 0) {
        int payload_type = 0;
        // 使用安全的字符串转换
        if (!safeStringToInt(attr_str, &payload_type)) {
          continue;  // 转换失败，跳过此属性
        }

        // 验证payload_type在有效范围内
        if (payload_type < 0 || payload_type > PT_UNDF_127) {
          continue;
        }

        minfo.rtpPayloadType = payload_type;
        rtpPort.payload_type = payload_type;

        // 安全地设置端口
        if (cli_port == NULL) {
          rtpPort.port_dst = ntohs(flow->tuple.port_dst);
        } else {
          int cli_port_int = 0;
          // 处理`-`分隔符
          // 52282-52283

          if (safeStringToInt(cli_port, &cli_port_int) && cli_port_int > 0 && cli_port_int <= 65535) {
            rtpPort.port_dst = (uint32_t)cli_port_int;
          } else {
            rtpPort.port_dst = ntohs(flow->tuple.port_dst);
          }
        }

        if (srv_port == NULL) {
          rtpPort.port_src = ntohs(flow->tuple.port_src);
        } else {
          int srv_port_int = 0;
          if (safeStringToInt(srv_port, &srv_port_int) && srv_port_int > 0 && srv_port_int <= 65535) {
            rtpPort.port_src = (uint32_t)srv_port_int;
          } else {
            rtpPort.port_src = ntohs(flow->tuple.port_src);
          }
        }

        // 对于静态payload类型（0-34），直接创建映射
        if (payload_type <= PT_H263) {
          minfo.rtspStream = flow;
          createMediaMapping(rtpPort, minfo, flow);
          continue;
        }

        // 处理rtpmap属性
        for (int i = 0; i < info.m_info_num; i++) {
          // 200OK 中 默认 media_attr 的第一个值为媒体参数
          int media_attr_rtp_map_num = info.m_info[i].a_attr_num;

          // 验证属性数量在合理范围内
          if (media_attr_rtp_map_num < 0 ||
              media_attr_rtp_map_num > SDP_M_ATTR_MAX_NUM) {
            continue;
          }
          for (int j = 0; j < media_attr_rtp_map_num; j++) {
            // 边界检查
            if (j >= SDP_M_ATTR_MAX_NUM) {
              break;
            }

            const char *media_attr_str = info.m_info[i].a_attributes[j];

            // 检查当前属性是否包含当前处理的媒体属性
            if (strstr(media_attr_str, attr_str) == NULL) {
              continue;
            }
            // 使用重构后的rtpmap处理函数
            processRtpMap(media_attr_str, minfo, rtpPort, flow, i);

            // 为RTSP创建特殊的流信息
            five_tuple tuple;
            memset(&tuple, 0, sizeof(tuple));
            memcpy(&tuple, &flow->tuple, sizeof(tuple));
            tuple.port_src = htons(rtpPort.port_dst);
            tuple.port_dst = htons(rtpPort.port_src);
            tuple.proto = 17;
            ipc_decoder *decoder = static_cast<ipc_decoder *>(flow->user);
            auto flow_info = decoder->findCreateFlow(&tuple);
            flow_info->real_protocol_id = IPC_PROTOCOL_RTP;
            flow_info->create_by = IPC_PROTOCOL_RTSP;
            flow_info->create_by_mediaType = minfo.mediaType;
            minfo.rtspStream = flow;
          }
        }
      }
    }
  }
}
void SdpInfo::setSipinfo(struct flow_info *flow, struct sip_request_info &sip_info) {
  if (cli_ip_.empty()) {
    char buff[64] = {0};
    get_iparray_to_string(buff, sizeof buff, flow->tuple.ip_src);
    std::string cli_ip(buff);
    cli_ip_ = cli_ip;
  }
  if (server_ip_.empty()) {
    char buff[64] = {0};
    get_iparray_to_string(buff, sizeof buff, flow->tuple.ip_dst);
    std::string server_ip(buff);
    server_ip_ = server_ip;
  }

  if (sip_info.status_code_val_ptr) {
    char buff[64] = {0};
    strncpy(buff, (const char *)sip_info.status_code_val_ptr, sip_info.status_code_val_len);
    std::string sip_status(buff);
    sip_status_ = sip_status;
  }

  if (sip_from_.empty() && strlen(sip_info.header_gather.from_line)) {
    std::string sip_from(sip_info.header_gather.from_line);
    sip_from_ = sip_from;
  }

  if (sip_to_.empty() && strlen(sip_info.header_gather.to_line)) {
    std::string sip_to(sip_info.header_gather.to_line);
    sip_to_ = sip_to;
  }
  if (strlen(sip_info.header_gather.user_agent) > 0) {
    std::string ua(sip_info.header_gather.user_agent);
    user_agent = ua;
  }
  sipMeidaDescription_v.clear();
}