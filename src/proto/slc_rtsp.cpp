
#include "slc_rtsp.h"
#include "ipc/ipc.h"
#include "ipc/ipc_decoder.h"
#include "slc_common.h"
#include "slc_rtp.h"
#include "base64.h"

#include <string.h>
#include <ctype.h>
#include <algorithm>
#include <vector>
#include <functional>
#include <map>
#include <memory>

static void rtsp_sdp_copy_one_item(char *result, int max_len, const uint8_t *value, uint16_t len) {
  int copy_len;
  copy_len = len;
  if (copy_len > max_len - 1)
    copy_len = max_len - 1;

  strncpy(result, (const char *)value, copy_len);
  result[copy_len] = 0;

  return;
}

static void dissect_rtsp_sdp_owner(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->o_username, sizeof(info->o_username), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->o_sessionid, sizeof(info->o_sessionid), line + index, black_index);
        break;
      case 2:
        rtsp_sdp_copy_one_item(info->o_version, sizeof(info->o_version), line + index, black_index);
        break;
      case 3:
        rtsp_sdp_copy_one_item(info->o_network_type, sizeof(info->o_network_type), line + index, black_index);
        break;
      case 4:
        rtsp_sdp_copy_one_item(info->o_address_type, sizeof(info->o_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(
              info->o_address, sizeof(info->o_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

static void dissect_rtsp_sdp_time(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_index;

  black_index = find_blank_space(line, line_len);
  if (black_index <= 0) {
    return;
  }

  rtsp_sdp_copy_one_item(info->t_time_start, sizeof(info->t_time_start), line, black_index);

  if (line_len > black_index + 1) {
    rtsp_sdp_copy_one_item(info->t_time_end, sizeof(info->t_time_end), line + black_index + 1, line_len - black_index - 1);
  }

  return;
}

static void dissect_rtsp_sdp_r_data(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->r_repeat_interval, sizeof(info->r_repeat_interval), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->r_active_duration, sizeof(info->r_active_duration), line + index, black_index);
        break;
      case 2:
        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(info->r_offsets_from_start_time, sizeof(info->r_offsets_from_start_time),
              line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }
  return;
}

static void dissect_rtsp_sdp_connnection_info(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->c_network_type, sizeof(info->c_network_type), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->c_address_type, sizeof(info->c_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(
              info->c_address, sizeof(info->c_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }
  return;
}

static void find_rstp_sdp_media_payload(const uint8_t *start, int len, char *result, int max_len) {
  int  black_index;
  int  offset = 0;
  char num_str[32];
  int  copy_len;
  int  num;
  int  _len = 0;

  while (offset < len) {
    black_index = find_blank_space(start + offset, len - offset);
    if (black_index <= 0) {
      break;
    }
    copy_len = black_index >= 32 ? 31 : black_index;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < RTSP_MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtsp_rtp_type_vals[num].strptr);
    }
    offset += black_index + 1;
  }

  if (offset < len) {
    copy_len = len - offset >= 32 ? 31 : len - offset;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < RTSP_MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtsp_rtp_type_vals[num].strptr);
    }
  }
}

static void dissect_rtsp_sdp_media(const uint8_t *line, int line_len, struct rtsp_sdp_m_info *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->m_type, sizeof(info->m_type), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->m_port, sizeof(info->m_port), line + index, black_index);
        break;
      case 2:
        rtsp_sdp_copy_one_item(info->m_proto, sizeof(info->m_proto), line + index, black_index);
        if (line_len > index + black_index + 1) {
          find_rstp_sdp_media_payload(
              line + index + black_index + 1, line_len - index - black_index - 1, info->m_payloads, sizeof(info->m_payloads));
        }

        return;  //return
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

int RtspStream::dissect_rtsp_sdp(const uint8_t *payload, const uint32_t payload_len, rtsp_sdp_info_t *info) {
  uint8_t        f_in_media = 0;
  uint32_t       offset = 0;
  const uint8_t *line;
  int            line_len;
  char           type;
  char           delim;

  int attr_index = 0;
  int media_index = -1;
  int a_index = 0;
  line = payload;
  while (offset < payload_len) {
    if (media_index >= RTSP_SDP_MEDIA_MAX_NUM - 1)
      break;
    line_len = find_packet_line_end(line, payload_len - offset);

    /*
        * Line must contain at least e.g. "v=".
        */
    if (line_len < 2)
      break;

    type = *line;
    delim = *(line + 1);
    if (delim != '=')
      goto next_line;

    switch (type) {
      case 'v':
        rtsp_sdp_copy_one_item(info->v_version, sizeof(info->v_version), line + 2, line_len - 2);
        break;
      case 'o':
        //rtsp_sdp_copy_one_item(info->o_owner, sizeof(info->o_owner), line + 2, line_len - 2);
        dissect_rtsp_sdp_owner(line + 2, line_len - 2, info);
        break;
      case 's':
        rtsp_sdp_copy_one_item(info->s_name, sizeof(info->s_name), line + 2, line_len - 2);
        break;
      case 'i':
        //if (f_in_media)
        //    rtsp_sdp_copy_one_item(info->m_info[media_index].m_title, sizeof(info->m_info[media_index].m_title), line + 2, line_len - 2);
        //else
        rtsp_sdp_copy_one_item(info->i_info, sizeof(info->i_info), line + 2, line_len - 2);
        break;
      case 'u':
        rtsp_sdp_copy_one_item(info->u_uri, sizeof(info->u_uri), line + 2, line_len - 2);
        break;
      case 'e':
        rtsp_sdp_copy_one_item(info->e_email, sizeof(info->e_email), line + 2, line_len - 2);
        break;
      case 'p':
        rtsp_sdp_copy_one_item(info->p_phone, sizeof(info->p_phone), line + 2, line_len - 2);
        break;
      case 'c':
        //rtsp_sdp_copy_one_item(info->c_info, sizeof(info->c_info), line + 2, line_len - 2);
        dissect_rtsp_sdp_connnection_info(line + 2, line_len - 2, info);
        break;
      case 'b':
        rtsp_sdp_copy_one_item(info->b_bandwidths, sizeof(info->b_bandwidths), line + 2, line_len - 2);
        break;
      case 't':
        //rtsp_sdp_copy_one_item(info->t_time, sizeof(info->t_time), line + 2, line_len - 2);
        dissect_rtsp_sdp_time(line + 2, line_len - 2, info);
        break;
      case 'r':
        //rtsp_sdp_copy_one_item(info->r_repeattime, sizeof(info->r_repeattime), line + 2, line_len - 2);
        dissect_rtsp_sdp_r_data(line + 2, line_len - 2, info);
        break;
      case 'z':
        rtsp_sdp_copy_one_item(info->z_timezone_adjustment, sizeof(info->z_timezone_adjustment), line + 2, line_len - 2);
        break;
      case 'k':
        rtsp_sdp_copy_one_item(info->k_encryptionkey, sizeof(info->k_encryptionkey), line + 2, line_len - 2);
        break;
      case 'm':
        f_in_media = 1;
        media_index++;
        attr_index = 0;
        //rtsp_sdp_copy_one_item(info->m_info[media_index].m_media, sizeof(info->m_info[media_index].m_media), line + 2, line_len - 2);
        dissect_rtsp_sdp_media(line + 2, line_len - 2, &info->m_info[media_index]);
        break;

      //now only one attributes is writing
      case 'a':
        if (f_in_media) {
          if (attr_index >= RTSP_SDP_M_ATTR_MAX_NUM)
            break;
          rtsp_sdp_copy_one_item(info->m_info[media_index].a_attributes[attr_index],
              sizeof(info->m_info[media_index].a_attributes[attr_index]), line + 2, line_len - 2);
          attr_index++;
        } else {
          if (a_index > 20)
            break;
          rtsp_sdp_copy_one_item(info->session_attribute[a_index], sizeof(info->session_attribute), line + 2, line_len - 2);
          a_index++;
        }
        break;
      default:
        break;
    }
  next_line:
    offset += line_len + 2;
    line = &payload[offset];
  }
  //    write_rtp_sdp_log(flow, direction, info);

  return 0;
}

int RtspStream::process_rtsp_reply_transport(const uint8_t *line, int linelen) {
  char marker_client_port[] = "client_port=";
  char marker_server_port[] = "server_port=";

  char *input = strndup((char *)line, linelen);
  char *token = strtok(input, ":;");
  while (token != NULL) {
    if (strncmp(token, marker_client_port, sizeof marker_client_port - 1) == 0) {
      strncpy(transport_client_port_, token + sizeof(marker_client_port) - 1, sizeof(transport_client_port_));
    }

    if (strncmp(token, marker_server_port, sizeof marker_server_port - 1) == 0) {
      strncpy(transport_server_port_, token + sizeof(marker_server_port) - 1, sizeof(transport_server_port_));
    }

    token = strtok(NULL, ":;");
  };

  if (strlen(transport_client_port_) > 0 && strlen(transport_server_port_) > 0) {
    transport_info_parsed_ = 1;
  }
  if (NULL != strstr((const char *)input, "RTP/AVP/TCP")) {
    rtp_over_rtsp_parsed_ = 1;
  }
  free(input);

  return 0;
}

void RtspStream::process_rtsp_reply(const uint8_t *line, int linelen) {
  const uint8_t *lineend = line + linelen;
  const uint8_t *status = line;
  const uint8_t *status_start;
  uint32_t       status_i;

  /* status code */

  /* Skip protocol/version */
  while (status < lineend && !isspace(*status)) status++;
  /* Skip spaces */
  while (status < lineend && isspace(*status)) status++;

  /* Actual code number now */
  status_start = status;
  status_i = 0;
  while (status < lineend && isdigit(*status)) status_i = status_i * 10 + *status++ - '0';

  struct rtsp_point_value *transport_line = &rstp_head_[EM_RSTP_H_TRANSPORT];
  if (transport_line->ptr != NULL) {
    /* eg: Transport: RTP/AVP/UDP;unicast;client_port=54782-54783;server_port=45020-45021;ssrc=1DCBB018 */
    process_rtsp_reply_transport(transport_line->ptr, transport_line->len);
  }

  status_code_ = status_i;
  response_code_ = status + 1;
  response_code_len_ = lineend > status ? lineend - status - 1 : 0;
  return;
}

void RtspStream::process_rtsp_request(const uint8_t *line, int linelen) {
  const uint8_t *lineend = line + linelen;
  uint32_t       ii;
  const uint8_t *url;
  const uint8_t *url_start;
  uint8_t       *tmp_url;

  const uint8_t *data = line;

  /* URL */
  url = line;
  /* Skip method name again */
  while (url < lineend && !isspace(*url)) url++;

  /* Skip spaces */
  while (url < lineend && isspace(*url)) url++;

  /* URL starts here */
  url_start = url;
  /* Scan to end of URL */
  while (url < lineend && !isspace(*url)) url++;

  request_uri_ = url_start;
  request_uri_len_ = url - url_start;

  return;
}
/*
* 解析rtsp头部的每一行头域，寻找空行位置
* 修复逻辑漏洞：确保在 head_len + sdp_len = payload_len 时能正确返回sdp_s
*/
const uint8_t *RtspStream::rtsp_parse_line_info(const uint8_t *payload, const uint16_t payload_len) {
  uint32_t a;
  uint16_t line_len = 0;
  uint8_t  parsed_lines = 0;
  const uint8_t          *line_ptr = payload;
  char                   *_header;
  struct rtsp_point_value ht_value;

  int            header_len;
  const uint8_t *sdp_s = NULL;
  int            i;

  // 参数检查
  if ((payload_len == 0) || (payload == NULL))
    return NULL;

  // 修复边界条件：使用 payload_len - 1 而不是 payload_len - 2
  // 确保能够处理到倒数第二个字节，以便检测 \r\n\r\n 序列
  for (a = 0; a <= payload_len - 2; a++) {
    // 检查是否找到 \r\n 序列
    if (a + 1 < payload_len && get_uint16_t(payload, a) == ntohs(0x0d0a)) {
      line_len = (uint16_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

      // 检查是否为空行（连续的 \r\n\r\n）
      if (line_len == 0) {
        // 空行后面就是SDP数据
        if (a + 2 < payload_len) {
          sdp_s = &payload[a + 2];
        } else if (a + 2 == payload_len) {
          // 边界情况：SDP数据长度为0，但位置有效
          sdp_s = &payload[a + 2];
        }
        break;
      }

      // 处理第一行（请求行或响应行）
      if (parsed_lines == 0) {
        rstp_head_[EM_RTSP_H_HEADER].len = line_len;
        rstp_head_[EM_RTSP_H_HEADER].ptr = line_ptr;
      } else {
        // 处理头部字段行
        header_len = find_special_char(line_ptr, line_len, ':');
        if (header_len <= 0) {
          goto next_line;
        }

        // 提取头部字段名
        if (line_ptr[header_len - 1] == ' ')
          _header = strndup((const char *)line_ptr, header_len - 1);
        else
          _header = strndup((const char *)line_ptr, header_len);

        if (_header == NULL) {
          goto next_line;
        }

        strdown_inplace(_header);

        // 检查头部字段值是否存在
        if (header_len + 1 >= line_len) {
          free(_header);
          goto next_line;
        }

        // 提取头部字段值
        if (line_ptr[header_len + 1] == ' ') {
          ht_value.len = line_len - header_len - 2;
          ht_value.ptr = line_ptr + header_len + 2;
        } else {
          ht_value.len = line_len - header_len - 1;
          ht_value.ptr = line_ptr + header_len + 1;
        }

        // 查找匹配的头部字段类型
        for (i = EM_RSTP_H_ACCEPT; i < EM_RTSP_H_MAX; i++) {
          if (strcmp(_header, rtsp_header_data[i].head_type) == 0) {
            rstp_head_[i].len = ht_value.len;
            rstp_head_[i].ptr = ht_value.ptr;
            break;
          }
        }

        free(_header);
      }

    next_line:
      parsed_lines++;
      line_ptr = &payload[a + 2];
      line_len = 0;

      // 检查是否已到达数据末尾
      if ((a + 2) >= payload_len)
        break;

      a++; /* 跳过 \n，下次循环将处理下一行 */
    }
  }

  return sdp_s;
}

uint8_t RtspStream::is_rtsp_request_or_reply(const uint8_t *line, int linelen, rtsp_type_t *type) {
  uint32_t       ii;
  const uint8_t *token, *next_token;
  int            tokenlen;
  char           response_chars[4];

  /* Is this an RTSP reply? */
  if (linelen >= 5 && strncasecmp("RTSP/", (const char *)line, 5) == 0) {
    *type = RTSP_REPLY;

    return 1;
  }

  /*
     * Is this an RTSP request?
     * Check whether the line begins with one of the RTSP request
     * methods.
     */
  for (ii = 0; ii < RTSP_NMETHODS; ii++) {
    int len = strlen(rtsp_methods[ii]);
    if (linelen >= len && strncasecmp(rtsp_methods[ii], (const char *)line, len) == 0 && (len == linelen || isspace(line[len]))) {
      *type = RTSP_REQUEST;

      strncpy(request_method_, rtsp_methods[ii], len);
      return 1;
    }
  }

  /* Wasn't a request or a response */
  *type = RTSP_NOT_FIRST_LINE;
  return 0;
}

void RtspStream::dissect_rtsp_conversation() {
  // 有client_port 与 server_port 的 建立与rtp的关联流
  if (transport_info_parsed_ == 1 || rtp_over_rtsp_parsed_ == 1) {
    if (transport_info_parsed_ == 1) {
      sdp_.SetRtspinfo(this, transport_client_port_, transport_server_port_);
    }
    if (rtp_over_rtsp_parsed_ == 1) {
      sdp_.SetRtspinfo(this, NULL, NULL);
    }
    int          sps_len = 0;
    char         sps[1024] = {0};
    int          pps_len = 0;
    char         pps[1024] = {0};
    ipc_decoder *decoder = static_cast<ipc_decoder *>(user);
    for (int i = 0; i < sdp_.info.m_info_num; i++) {
      for (int j = 0; j < sdp_.info.m_info[i].a_attr_num; j++) {
        char *sps_start = strstr(sdp_.info.m_info[i].a_attributes[j], "sprop-parameter-sets=");
        if (NULL != sps_start) {
          char *split = strchr(sps_start, ',');
          if (split) {
            sps_len = base64_decode(sps_start + 21, split - sps_start - 21, (unsigned char *)sps);
            pps_len = base64_decode(split + 1, strlen(split) - 1, (unsigned char *)pps);
          }
        }
      }
    }
    memset(sps_, 0, sizeof sps_);
    memset(pps_, 0, sizeof pps_);

    memcpy(sps_, sps, sps_len);
    sps_len_ = sps_len;

    memcpy(pps_, pps, pps_len);
    pps_len_ = pps_len;
    transport_info_parsed_ = 0;
    rtp_over_rtsp_parsed_ = 0;
    //
  }
}
int RtspStream::dissect_rtspmessage(const uint8_t *payload, const uint32_t payload_len) {
  int            first_linelen;
  const uint8_t *line;
  uint8_t        is_request_or_reply;
  rtsp_type_t    rtsp_type_packet;

  uint8_t body_requires_content_len;

  gpointer             ht_value = NULL;
  const uint8_t       *sdp_data = NULL;
  struct header_value *value;

  line = payload;
  first_linelen = find_packet_line_end(payload, payload_len);
  is_request_or_reply = is_rtsp_request_or_reply(line, first_linelen, &rtsp_type_packet);

  if (rtsp_type_packet != RTSP_NOT_FIRST_LINE) {
    body_requires_content_len = 1;
  } else {
    body_requires_content_len = 0;
    return -1;
  }

  /* 解析rtsp每行的 头部域，并将数据 插入hash表 */
  sdp_data = rtsp_parse_line_info(payload, payload_len);

  /*查看头部行是request 还是response         */
  //if (ht_value) {
  if (rstp_head_[EM_RTSP_H_HEADER].len > 0 && rstp_head_[EM_RTSP_H_HEADER].ptr != NULL) {
    char transfer[64] = {0};
    value = (struct header_value *)ht_value;
    memcpy(transfer, rstp_head_[EM_RTSP_H_HEADER].ptr,
        rstp_head_[EM_RTSP_H_HEADER].len >= sizeof(transfer) ? sizeof(transfer) - 1 : rstp_head_[EM_RTSP_H_HEADER].len);
    switch (rtsp_type_packet) {
      case RTSP_REQUEST:
        process_rtsp_request(rstp_head_[EM_RTSP_H_HEADER].ptr, rstp_head_[EM_RTSP_H_HEADER].len);
        break;

      case RTSP_REPLY:
        process_rtsp_reply(rstp_head_[EM_RTSP_H_HEADER].ptr, rstp_head_[EM_RTSP_H_HEADER].len);
        break;

      case RTSP_NOT_FIRST_LINE:
        /* Drop through, it may well be a header line */
        break;
    }
  }
  memset(&rstp_head_, 0, sizeof(rstp_head_));
  /* 解析sdp 数据*/
  if (sdp_data) {
    int            len_offset = sdp_data - payload;
    const uint32_t sdp_len = payload_len - len_offset;
    //printf("sdp  data_len:%d\n",sdp_len);
    dissect_rtsp_sdp(sdp_data, sdp_len, &sdp_info_);
    sdp_.dissect_sdp(this, 0, sdp_data, sdp_len);
  }
  dissect_rtsp_conversation();
  return 0;
}
int RtspStream::is_rtsp_message(const uint8_t *payload, int payload_len) {
  uint32_t       ii;
  const uint8_t *token, *next_token;
  int            tokenlen;
  char           response_chars[4];

  /* Is this an RTSP reply? */
  if (payload_len >= 5 && strncasecmp("RTSP/", (const char *)payload, 5) == 0) {
    return 1;
  }

  /*
     * Is this an RTSP request?
     * Check whether the line begins with one of the RTSP request
     * methods.
     */
  for (ii = 0; ii < RTSP_NMETHODS; ii++) {
    int len = strlen(rtsp_methods[ii]);
    if (payload_len >= len && strncasecmp(rtsp_methods[ii], (const char *)payload, len) == 0 &&
        (len == payload_len || isspace(payload[len]))) {
      return 1;
    }
  }
  return 0;
}

int RtspStream::is_rtp_over_rtsp(const uint8_t *payload, int payload_len) {
  if (payload[0] == RTSP_FRAMEHDR) { /* RTP/RTCP */
    return 1;
  }
  return 0;
}

int RtspStream::dissect_rtp_over_rtsp(const uint8_t *payload, const uint32_t payload_len) {
  int hl = 0;
  dissect_rtsp_conversation();
  RTSPInterleavedFrame *frame = (RTSPInterleavedFrame *)payload;
  hl = ntohs(frame->len);
  RTPKEEPER->dissectProto(this, 0, payload + sizeof(RTSPInterleavedFrame), payload_len - sizeof(RTSPInterleavedFrame));
  return 0;
}

#define RTSP_NEED_MORE 0
#define RTSP_ERROR -1

// 返回 RTSP head 长度范围
// 负数: 没有找到
int64_t RtspStream::get_header_len(const uint8_t *p, int l) {
  if (0 == is_rtsp_message(p, l)) {
    return RTSP_ERROR;
  }

  const uint8_t *find = (const uint8_t *)memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
  if (NULL == find) {
    return RTSP_NEED_MORE;
  }

  return find - p + 4;
}

// 返回 Content-Length 数值类型
int64_t RtspStream::get_contentlen(const uint8_t *p, int l) {
#define CONTENT_STR_LEN 16
  const char *str_start = "\r\nContent-Length";
  const char *str_end = "\r\n";

  if (l < CONTENT_STR_LEN)
    return 0;

  const uint8_t *find_start = (const uint8_t *)memmem(p, l, str_start, CONTENT_STR_LEN);
  if (find_start)
    find_start += CONTENT_STR_LEN;
  else
    return 0;

  const uint8_t *find_end = (const uint8_t *)memmem(find_start, l - (find_start - p), str_end, strlen((char *)str_end));
  if (find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
    return 0;

  int  i = 0;
  char buff[16];
  while (find_start < find_end) {
    if (isdigit(*find_start))
      buff[i++] = *find_start;
    else if (*find_start != ':' && *find_start != ' ')
      return 0;
    find_start++;
  }
  buff[i] = 0;

  return atol(buff);
}

int RtspStream::get_rtsp_len(const uint8_t *payload, int payload_len, rtsp_type_t *type) {
  int64_t hl = 0;  // header  len
  int64_t cl = 0;  // content len
  int     offset = 0;
  offset = find_special_char(payload, payload_len, '$');
  if (offset >= 0) {
    *type = RTSP_RTP_OVER;
    RTSPInterleavedFrame *frame = (RTSPInterleavedFrame *)(payload + offset);
    hl = ntohs(frame->len) + sizeof(RTSPInterleavedFrame);
    if (hl > payload_len) {
      return RTSP_NEED_MORE;
    }
    if (*(payload + hl) == '$') {
      return hl;
    }
    return hl;
  }
  hl = get_header_len(payload, payload_len);
  if (RTSP_NEED_MORE == hl) {
    return RTSP_NEED_MORE;
  } else if (RTSP_ERROR == hl) {
    return RTSP_ERROR;
  }
  *type = RTSP_MESSAGE;
  cl = get_contentlen(payload, hl);
  if (cl > 0) {
    return hl + cl;
  }
  return hl;
}

int RtspStream::enqueRtspPayload(uint8_t C2S, const uint8_t *payload, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  //不考虑开头不为RTSP消息的报文 原因是 识别不出不会进入重组
  //进行应用层重组
  uint8_t    *p = (uint8_t *)payload;
  int         hl = 0;
  int         l = len;
  int         offset = 0;
  auto        c = cache + C2S;
  rtsp_type_t type = RTSP_UNKNOWN;
  if (!c->empty()) {
    if (1 == is_rtsp_message(payload, len)) {
      dissect_rtspmessage(c->data(), c->size());
      c->clear();
    }
    //正常 拼装
    std::copy(&payload[0], &payload[len], std::back_inserter(*c));
    p = c->data();
    l = c->size();
  }
  // 专业切割机
  while (offset < l) {
    hl = get_rtsp_len((const uint8_t *)p + offset, l - offset, &type);
    if (hl > 0 && l - offset >= hl) {
      if (type == RTSP_MESSAGE) {
        dissect_rtspmessage((const uint8_t *)p + offset, (uint32_t)hl);
      } else if (type == RTSP_RTP_OVER) {
        dissect_rtp_over_rtsp((const uint8_t *)p + offset, (uint32_t)hl);
      }
      offset += hl;
    } else if (hl == RTSP_ERROR) {
      goto RTSP_DROP;
    } else if (hl == RTSP_NEED_MORE) {
      break;
    } else if (hl > l - offset) {
      break;
    }
  }

  // 有没有剩料?
  if (offset >= 0 && offset < l) {
    if (TRUE != c->empty() && offset > 0)  //已开启缓存, 直接将剩料挪到前面
    {
      c->erase(c->begin(), c->begin() + offset);
    } else if (TRUE == c->empty())  //未开启缓存, 创建缓存, 把剩料放在前面
    {
      //正常 拼装
      std::copy(p + offset, p + offset + (l - offset), std::back_inserter(*c));
    }
    goto RTSP_NEED_MORE_PKT;
  } else {
    if (TRUE != c->empty()) {
      c->clear();
    }
    goto RTSP_NEED_MORE_PKT;
  }

  // RTSP  太长, 只解析被缓存的部分.
RTSP_DROP:
  if (TRUE != c->empty()) {
    c->clear();
  }

// RTSP 解析,需要更多报文
RTSP_NEED_MORE_PKT:
  return 0;
}
auto rtspkeeper = RTSPKEEPER;

RtspKeeper::RtspKeeper() {
  setRegCallback(IPC_PROTOCOL_RTSP, "rtsp",
      std::bind(&RtspKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtspKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtspKeeper::dissectProto_rsm, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4, std::placeholders::_5, std::placeholders::_6, std::placeholders::_7),
      std::bind(
          &RtspKeeper::miss, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
}

int RtspKeeper::dissectProto_rsm(
    void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  if (!(ptr && len))
    return -1;

  uint32_t                    offset = 0;
  flow_info                  *flow = static_cast<flow_info *>(user);
  std::shared_ptr<RtspStream> stream = nullptr;

  auto it = map_streams_.find(flow);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    auto newStreamPtr = std::make_shared<RtspStream>(flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<RtspStream>()");
      return -1;
    }
    stream = newStreamPtr;
    map_streams_[flow] = newStreamPtr;
  }
  stream->enqueRtspPayload(C2S, ptr, len, seq, ack, flg);
  return 0;
}
void RtspKeeper::dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  return;  //没重组的情况下不符合格式，暂时不写
}

void RtspKeeper::identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  int line_len = 0;

  line_len = find_packet_line_end(payload, payload_len);

  if (line_len < 10)
    return;

  if (strncasecmp((const char *)payload, "RTSP/1.0", 8) == 0 ||
      strncasecmp((const char *)payload + line_len - 9, " RTSP/1.0", 9) == 0) {
    flow->real_protocol_id = IPC_PROTOCOL_RTSP;
    global_logger.Info("identifyProto = RTSP");
  }
  if (payload[0] == RTSP_FRAMEHDR) { /* RTP/RTCP */
    flow->real_protocol_id = IPC_PROTOCOL_RTSP;
    global_logger.Info("identifyProto = RTSP");
  }
  return;
}